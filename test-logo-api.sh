#!/bin/bash

# Test script for Logo Management API
# This script tests all the logo API endpoints

BASE_URL="http://localhost/api/dashboard/logo"
TEST_IMAGE="test-logo.png"

echo "Logo API Test Script"
echo "==================="
echo ""

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo "✓ $2"
    else
        echo "✗ $2"
    fi
}

# Function to create a test image
create_test_image() {
    echo "Creating test image..."
    # Create a simple 100x100 PNG image using ImageMagick (if available)
    if command -v convert &> /dev/null; then
        convert -size 100x100 xc:blue -fill white -gravity center -pointsize 20 -annotate +0+0 "TEST" "$TEST_IMAGE"
        echo "✓ Test image created: $TEST_IMAGE"
    else
        echo "⚠ ImageMagick not found. Please create a test image named '$TEST_IMAGE' manually."
        echo "  You can use any PNG/JPEG image file for testing."
        read -p "Press Enter when you have created the test image..."
    fi
}

# Function to cleanup test files
cleanup() {
    if [ -f "$TEST_IMAGE" ]; then
        rm "$TEST_IMAGE"
        echo "✓ Test image cleaned up"
    fi
}

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo "✗ curl is required but not installed. Please install curl and try again."
    exit 1
fi

# Check if the service is running
echo "Checking if dashboard service is running..."
curl -s "$BASE_URL/../health" > /dev/null
if [ $? -eq 0 ]; then
    echo "✓ Dashboard service is running"
else
    echo "✗ Dashboard service is not accessible. Please ensure the application is running."
    echo "  Run: docker-compose up -d"
    exit 1
fi

# Create test image if it doesn't exist
if [ ! -f "$TEST_IMAGE" ]; then
    create_test_image
fi

if [ ! -f "$TEST_IMAGE" ]; then
    echo "✗ Test image not found. Cannot proceed with tests."
    exit 1
fi

echo ""
echo "Starting API Tests..."
echo "--------------------"

# Test 1: Upload Logo
echo ""
echo "Test 1: Upload Logo"
UPLOAD_RESPONSE=$(curl -s -X POST -F "logo=@$TEST_IMAGE" "$BASE_URL/upload")
UPLOAD_STATUS=$?
print_result $UPLOAD_STATUS "Upload request sent"

if [ $UPLOAD_STATUS -eq 0 ]; then
    echo "Response: $UPLOAD_RESPONSE"
    # Extract filename from response (basic JSON parsing)
    FILENAME=$(echo "$UPLOAD_RESPONSE" | grep -o '"filename":"[^"]*"' | cut -d'"' -f4)
    if [ ! -z "$FILENAME" ]; then
        echo "✓ Uploaded filename: $FILENAME"
    else
        echo "⚠ Could not extract filename from response"
    fi
fi

# Test 2: Get Logo List
echo ""
echo "Test 2: Get Logo List"
LIST_RESPONSE=$(curl -s "$BASE_URL/list")
LIST_STATUS=$?
print_result $LIST_STATUS "Logo list request sent"

if [ $LIST_STATUS -eq 0 ]; then
    echo "Response: $LIST_RESPONSE"
    # Check if response contains data
    if echo "$LIST_RESPONSE" | grep -q '"success":true'; then
        echo "✓ Logo list retrieved successfully"
    else
        echo "⚠ Logo list request may have failed"
    fi
fi

# Test 3: Get Active Logo
echo ""
echo "Test 3: Get Active Logo"
ACTIVE_RESPONSE=$(curl -s "$BASE_URL/current/active")
ACTIVE_STATUS=$?
print_result $ACTIVE_STATUS "Active logo request sent"

if [ $ACTIVE_STATUS -eq 0 ]; then
    echo "Response: $ACTIVE_RESPONSE"
    if echo "$ACTIVE_RESPONSE" | grep -q '"success":true'; then
        echo "✓ Active logo retrieved successfully"
    else
        echo "⚠ Active logo request may have failed"
    fi
fi

# Test 4: Get Specific Logo (if we have a filename)
if [ ! -z "$FILENAME" ]; then
    echo ""
    echo "Test 4: Get Specific Logo"
    curl -s -I "$BASE_URL/$FILENAME" > /tmp/logo_headers.txt
    LOGO_STATUS=$?
    print_result $LOGO_STATUS "Logo image request sent"
    
    if [ $LOGO_STATUS -eq 0 ]; then
        if grep -q "200 OK" /tmp/logo_headers.txt; then
            echo "✓ Logo image served successfully"
            echo "Content-Type: $(grep -i "content-type" /tmp/logo_headers.txt)"
        else
            echo "⚠ Logo image request may have failed"
        fi
    fi
    rm -f /tmp/logo_headers.txt
fi

# Test 5: Update Logo (if we have a filename)
if [ ! -z "$FILENAME" ]; then
    echo ""
    echo "Test 5: Update Logo"
    UPDATE_RESPONSE=$(curl -s -X PUT -F "logo=@$TEST_IMAGE" "$BASE_URL/$FILENAME")
    UPDATE_STATUS=$?
    print_result $UPDATE_STATUS "Logo update request sent"
    
    if [ $UPDATE_STATUS -eq 0 ]; then
        echo "Response: $UPDATE_RESPONSE"
        if echo "$UPDATE_RESPONSE" | grep -q '"success":true'; then
            echo "✓ Logo updated successfully"
        else
            echo "⚠ Logo update may have failed"
        fi
    fi
fi

# Test 6: Delete Logo (if we have a filename)
if [ ! -z "$FILENAME" ]; then
    echo ""
    echo "Test 6: Delete Logo"
    DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/$FILENAME")
    DELETE_STATUS=$?
    print_result $DELETE_STATUS "Logo delete request sent"
    
    if [ $DELETE_STATUS -eq 0 ]; then
        echo "Response: $DELETE_RESPONSE"
        if echo "$DELETE_RESPONSE" | grep -q '"success":true'; then
            echo "✓ Logo deleted successfully"
        else
            echo "⚠ Logo delete may have failed"
        fi
    fi
fi

# Test 7: Error Handling - Try to get deleted logo
if [ ! -z "$FILENAME" ]; then
    echo ""
    echo "Test 7: Error Handling (Get Deleted Logo)"
    ERROR_RESPONSE=$(curl -s "$BASE_URL/$FILENAME")
    ERROR_STATUS=$?
    print_result $ERROR_STATUS "Error handling request sent"
    
    if [ $ERROR_STATUS -eq 0 ]; then
        if echo "$ERROR_RESPONSE" | grep -q '"success":false'; then
            echo "✓ Error handling works correctly"
        else
            echo "⚠ Error handling may not be working as expected"
        fi
        echo "Response: $ERROR_RESPONSE"
    fi
fi

# Test 8: Invalid File Upload
echo ""
echo "Test 8: Invalid File Upload"
echo "This is not an image" > test-invalid.txt
INVALID_RESPONSE=$(curl -s -X POST -F "logo=@test-invalid.txt" "$BASE_URL/upload")
INVALID_STATUS=$?
print_result $INVALID_STATUS "Invalid file upload request sent"

if [ $INVALID_STATUS -eq 0 ]; then
    if echo "$INVALID_RESPONSE" | grep -q '"success":false'; then
        echo "✓ Invalid file rejection works correctly"
    else
        echo "⚠ Invalid file rejection may not be working"
    fi
    echo "Response: $INVALID_RESPONSE"
fi
rm -f test-invalid.txt

echo ""
echo "==================="
echo "API Tests Completed"
echo "==================="
echo ""
echo "Summary:"
echo "- All endpoints have been tested"
echo "- Check the responses above for any issues"
echo "- Green checkmarks (✓) indicate successful requests"
echo "- Yellow warnings (⚠) may indicate response parsing issues"
echo "- Red X marks (✗) indicate request failures"
echo ""
echo "If you see any failures, check:"
echo "1. Docker containers are running: docker-compose ps"
echo "2. Service logs: docker logs dashboardservice"
echo "3. Network connectivity: curl http://localhost/api/dashboard/health"

# Cleanup
cleanup

echo ""
echo "Test completed!"
