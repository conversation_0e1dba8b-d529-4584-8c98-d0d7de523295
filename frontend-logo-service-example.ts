// Example Angular service for logo management
// Place this in your Angular frontend project (e.g., src/app/services/logo.service.ts)

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface LogoInfo {
  filename: string;
  originalName: string;
  mimetype?: string;
  size: number;
  path: string;
  uploadDate: Date;
  updateDate?: Date;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  count?: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class LogoService {
  private readonly baseUrl = '/api/dashboard/logo'; // Adjust based on your nginx proxy configuration

  constructor(private http: HttpClient) {}

  /**
   * Upload a new logo image
   * @param file - The image file to upload
   * @returns Observable with upload result
   */
  uploadLogo(file: File): Observable<LogoInfo> {
    const formData = new FormData();
    formData.append('logo', file);

    return this.http.post<ApiResponse<LogoInfo>>(`${this.baseUrl}/upload`, formData)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Upload failed');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get list of all uploaded logos
   * @returns Observable with array of logo information
   */
  getLogoList(): Observable<LogoInfo[]> {
    return this.http.get<ApiResponse<LogoInfo[]>>(`${this.baseUrl}/list`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Failed to get logo list');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get the currently active logo (most recent)
   * @returns Observable with active logo information
   */
  getActiveLogo(): Observable<LogoInfo> {
    return this.http.get<ApiResponse<LogoInfo>>(`${this.baseUrl}/current/active`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'No active logo found');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Update/replace an existing logo
   * @param filename - The filename of the logo to replace
   * @param file - The new image file
   * @returns Observable with update result
   */
  updateLogo(filename: string, file: File): Observable<LogoInfo> {
    const formData = new FormData();
    formData.append('logo', file);

    return this.http.put<ApiResponse<LogoInfo>>(`${this.baseUrl}/${filename}`, formData)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            return response.data;
          }
          throw new Error(response.message || 'Update failed');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Delete a logo
   * @param filename - The filename of the logo to delete
   * @returns Observable with deletion result
   */
  deleteLogo(filename: string): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.baseUrl}/${filename}`)
      .pipe(
        map(response => {
          if (response.success) {
            return true;
          }
          throw new Error(response.message || 'Delete failed');
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get the URL for displaying a logo image
   * @param filename - The filename of the logo
   * @returns The full URL to the logo image
   */
  getLogoUrl(filename: string): string {
    return `${this.baseUrl}/${filename}`;
  }

  /**
   * Validate file before upload
   * @param file - The file to validate
   * @returns Validation result with error message if invalid
   */
  validateLogoFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { valid: true };
  }

  /**
   * Handle HTTP errors
   * @param error - The HTTP error response
   * @returns Observable error
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }

    console.error('Logo Service Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}

// Example Angular component usage:
/*
import { Component } from '@angular/core';
import { LogoService, LogoInfo } from './services/logo.service';

@Component({
  selector: 'app-logo-manager',
  template: `
    <div class="logo-manager">
      <h2>Logo Management</h2>
      
      <!-- File Upload -->
      <div class="upload-section">
        <input type="file" #fileInput (change)="onFileSelected($event)" accept="image/*">
        <button (click)="uploadLogo()" [disabled]="!selectedFile">Upload Logo</button>
      </div>
      
      <!-- Current Logo Display -->
      <div class="current-logo" *ngIf="activeLogo">
        <h3>Current Logo</h3>
        <img [src]="logoService.getLogoUrl(activeLogo.filename)" 
             [alt]="activeLogo.originalName" 
             style="max-width: 200px; max-height: 200px;">
        <p>Uploaded: {{activeLogo.uploadDate | date}}</p>
      </div>
      
      <!-- Logo List -->
      <div class="logo-list">
        <h3>All Logos</h3>
        <div *ngFor="let logo of logoList" class="logo-item">
          <img [src]="logoService.getLogoUrl(logo.filename)" 
               [alt]="logo.originalName" 
               style="width: 100px; height: 100px; object-fit: cover;">
          <p>{{logo.originalName}}</p>
          <button (click)="deleteLogo(logo.filename)">Delete</button>
        </div>
      </div>
    </div>
  `
})
export class LogoManagerComponent {
  selectedFile: File | null = null;
  activeLogo: LogoInfo | null = null;
  logoList: LogoInfo[] = [];

  constructor(public logoService: LogoService) {
    this.loadActiveLogo();
    this.loadLogoList();
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const validation = this.logoService.validateLogoFile(file);
      if (validation.valid) {
        this.selectedFile = file;
      } else {
        alert(validation.error);
        this.selectedFile = null;
      }
    }
  }

  uploadLogo(): void {
    if (this.selectedFile) {
      this.logoService.uploadLogo(this.selectedFile).subscribe({
        next: (result) => {
          console.log('Logo uploaded:', result);
          this.loadActiveLogo();
          this.loadLogoList();
          this.selectedFile = null;
        },
        error: (error) => {
          console.error('Upload error:', error);
          alert('Upload failed: ' + error.message);
        }
      });
    }
  }

  loadActiveLogo(): void {
    this.logoService.getActiveLogo().subscribe({
      next: (logo) => this.activeLogo = logo,
      error: (error) => console.log('No active logo found')
    });
  }

  loadLogoList(): void {
    this.logoService.getLogoList().subscribe({
      next: (logos) => this.logoList = logos,
      error: (error) => console.error('Failed to load logo list:', error)
    });
  }

  deleteLogo(filename: string): void {
    if (confirm('Are you sure you want to delete this logo?')) {
      this.logoService.deleteLogo(filename).subscribe({
        next: () => {
          this.loadActiveLogo();
          this.loadLogoList();
        },
        error: (error) => {
          console.error('Delete error:', error);
          alert('Delete failed: ' + error.message);
        }
      });
    }
  }
}
*/
