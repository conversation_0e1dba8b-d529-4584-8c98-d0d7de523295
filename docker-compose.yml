version: '3.8'
services:
  frontend:
    build: ./frontend
    container_name: frontend
    env_file:
      - ./frontend/.env
    ports:
      - '8080:80'
    depends_on:
      - nginx
    networks:
      - myshopcnc-net

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: timescale
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: root@123
    volumes:
      - timescale_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
    networks:
      - myshopcnc-net

  dashboardservice:
    build: ./backend/dashboardservice
    container_name: dashboardservice
    env_file:
      - ./backend/.env
    environment:
      - DB_HOST=timescaledb
    volumes:
      - logo_uploads:/app/uploads
    depends_on:
      - timescaledb
    networks:
      - myshopcnc-net

  userservice:
    build: ./backend/userservice
    container_name: userservice
    env_file:
      - ./backend/.env
    environment:
      - DB_HOST=timescaledb
    depends_on:
      - timescaledb
    networks:
      - myshopcnc-net

  nginx:
    build: ./nginx
    container_name: nginx-proxy
    ports:
      - '80:80'
    depends_on:
      - dashboardservice
      - userservice
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    networks:
      - myshopcnc-net

volumes:
  timescale_data:
    driver: local
  logo_uploads:
    driver: local

networks:
  myshopcnc-net:
    driver: bridge
