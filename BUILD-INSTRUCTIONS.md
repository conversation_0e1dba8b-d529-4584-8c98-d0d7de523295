# MyShopCNC Build and Installer Instructions

This document provides complete instructions for building the MyShopCNC project and creating a Windows installer.

## Project Overview

MyShopCNC is a Machine Tool Health Monitoring Solution with the following components:
- **Angular Frontend** (Port 8080)
- **Node.js Backend Services** (Dashboard & User services)
- **Nginx Reverse Proxy** (Port 80)
- **TimescaleDB Database** (Port 5432)

## Build Process Completed

✅ **Project Built Successfully**
- All Docker images have been built
- Images exported to `docker-images/` directory
- Total size: ~3.4GB

### Built Components:
1. **Frontend** (59MB) - Angular app with Nginx
2. **Nginx** (188MB) - Reverse proxy
3. **Dashboard Service** (1.1GB) - Node.js backend
4. **User Service** (1.1GB) - Node.js backend  
5. **TimescaleDB** (1.1GB) - Database

## Windows Installer Created

✅ **Installer Components Ready**
- Inno Setup script: `installer/MyShopCNC-Setup.iss`
- PowerShell scripts for Docker management
- Batch file wrappers for easy execution
- Installation validation tools

### Installer Features:
- **Docker Desktop Check** - Verifies Docker is installed
- **Complete Package** - Includes all Docker images (3.4GB)
- **Easy Management** - Start/Stop shortcuts created
- **Validation Tools** - Check installation integrity
- **Uninstall Support** - Clean removal process

## Next Steps

### To Build the Installer:

1. **Install Inno Setup 6.0+**
   ```
   Download from: https://jrsoftware.org/isinfo.php
   ```

2. **Build the Installer**
   ```bash
   cd installer
   build-installer.bat
   ```
   
   Or manually:
   - Open `installer/MyShopCNC-Setup.iss` in Inno Setup Compiler
   - Click "Build" or press F9
   - Installer will be created in `installer/output/`

3. **Distribute**
   - Installer: `MyShopCNC-Setup-1.0.0.exe` (~3.4GB)
   - Users need Windows 10+ and Docker Desktop

### For End Users:

1. **Install Docker Desktop** (if not already installed)
2. **Run Installer** as Administrator
3. **Load Docker Images** (done automatically or via shortcut)
4. **Start Application** using desktop shortcut
5. **Access at** http://localhost:8080

## File Structure

```
MyShopCNC-dev/
├── docker-compose.yml          # Main orchestration
├── .env                        # Environment variables
├── frontend/                   # Angular application
├── backend/                    # Node.js services
├── nginx/                      # Reverse proxy config
├── timescaledb/               # Database config
├── docker-images/             # Exported images (3.4GB)
│   ├── frontend.tar
│   ├── nginx.tar
│   ├── dashboardservice.tar
│   ├── userservice.tar
│   └── timescaledb.tar
└── installer/                 # Windows installer
    ├── MyShopCNC-Setup.iss    # Inno Setup script
    ├── scripts/               # Management scripts
    ├── license.txt            # License agreement
    └── output/                # Built installer
```

## Testing the Installation

### Local Testing:
1. Build installer using `build-installer.bat`
2. Run installer on clean Windows machine
3. Verify all shortcuts work
4. Test application startup and access
5. Use validation script to check components

### Validation Commands:
```bash
# Check installation
validate-installation.bat

# Manual checks
docker images                  # Verify images loaded
docker-compose ps             # Check running containers
curl http://localhost:8080    # Test frontend access
```

## Troubleshooting

### Build Issues:
- **Docker not running**: Start Docker Desktop
- **Images missing**: Run `docker-compose build` first
- **Inno Setup errors**: Check file paths in .iss script

### Installation Issues:
- **Docker not found**: Install Docker Desktop first
- **Permission errors**: Run installer as Administrator
- **Port conflicts**: Stop other services on ports 80/8080

### Runtime Issues:
- **Containers won't start**: Check Docker Desktop memory allocation
- **Database connection**: Verify TimescaleDB container is running
- **Frontend not loading**: Check nginx container and port mapping

## Performance Recommendations

### Development:
- **RAM**: 16GB+ recommended for building
- **Storage**: SSD for better Docker performance
- **CPU**: Multi-core for parallel builds

### Production:
- **RAM**: 8GB+ for running containers
- **Storage**: 10GB+ free space
- **Network**: Stable connection for Docker pulls

## Security Considerations

### Default Configuration:
- Database password: `root@123` (change in production)
- No HTTPS (add SSL certificates for production)
- Default ports exposed (configure firewall as needed)

### Production Hardening:
1. Change default passwords in `.env` files
2. Configure SSL/TLS certificates
3. Set up proper firewall rules
4. Use Docker secrets for sensitive data
5. Regular security updates

## Support and Maintenance

### Logs:
```bash
# Application logs
docker-compose logs

# Individual service logs
docker-compose logs frontend
docker-compose logs dashboardservice
```

### Updates:
1. Rebuild Docker images with new code
2. Export updated images
3. Rebuild installer
4. Distribute new installer version

### Backup:
- Export Docker volumes for data persistence
- Backup configuration files
- Document any customizations

## Success! 🎉

The MyShopCNC project has been successfully built and packaged for Windows installation. The installer includes:

- ✅ Complete application stack
- ✅ All Docker images (3.4GB)
- ✅ Management scripts
- ✅ Validation tools
- ✅ User-friendly shortcuts
- ✅ Comprehensive documentation

Users can now install and run MyShopCNC on Windows with Docker Desktop!
