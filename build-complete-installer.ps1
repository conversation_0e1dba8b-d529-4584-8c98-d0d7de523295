# MyShopCNC Complete Build and Installer Creation Script (PowerShell)
# This script automates the entire process from building Docker images to creating the Windows installer

param(
    [switch]$SkipBuild,
    [switch]$SkipExport,
    [switch]$SkipInstaller,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Configuration
$ProjectName = "MyShopCNC"
$ProjectVersion = "1.0.0"
$DockerImagesDir = "docker-images"
$InstallerDir = "installer"
$OutputDir = Join-Path $InstallerDir "output"
$BuildLog = "build.log"

# Initialize
$StartTime = Get-Date
$ErrorCount = 0
$WarningCount = 0

# Logging function
function Write-Log {
    param($Message, $Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Add-Content -Path $BuildLog -Value $logEntry
    
    switch ($Level) {
        "ERROR" { Write-Host "✗ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠ $Message" -ForegroundColor Yellow }
        "SUCCESS" { Write-Host "✓ $Message" -ForegroundColor Green }
        default { Write-Host $Message -ForegroundColor White }
    }
}

# Error handling
function Handle-Error {
    param($Message)
    Write-Log $Message "ERROR"
    $script:ErrorCount++
}

function Handle-Warning {
    param($Message)
    Write-Log $Message "WARNING"
    $script:WarningCount++
}

# Header
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "MyShopCNC Complete Build and Installer Creation Script" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

Write-Log "Build started" "INFO"

# Step 1: Verify Prerequisites
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "STEP 1: VERIFYING PREREQUISITES" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

try {
    # Check Docker
    Write-Host "Checking Docker installation..." -ForegroundColor Yellow
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Docker is installed: $dockerVersion" "SUCCESS"
    } else {
        Handle-Error "Docker is not installed or not in PATH. Please install Docker Desktop."
        throw "Docker not found"
    }

    # Check Docker daemon
    Write-Host "Checking Docker daemon..." -ForegroundColor Yellow
    docker info 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Docker daemon is running" "SUCCESS"
    } else {
        Handle-Error "Docker daemon is not running. Please start Docker Desktop."
        throw "Docker daemon not running"
    }

    # Check Docker Compose
    Write-Host "Checking Docker Compose..." -ForegroundColor Yellow
    $composeVersion = docker-compose --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Docker Compose is available: $composeVersion" "SUCCESS"
    } else {
        Handle-Error "Docker Compose is not available"
        throw "Docker Compose not found"
    }

    # Check Inno Setup
    Write-Host "Checking Inno Setup installation..." -ForegroundColor Yellow
    $innoSetupPath = $null
    $possiblePaths = @(
        "${env:ProgramFiles(x86)}\Inno Setup 6\ISCC.exe",
        "${env:ProgramFiles}\Inno Setup 6\ISCC.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $innoSetupPath = $path
            break
        }
    }
    
    if ($innoSetupPath) {
        Write-Log "Inno Setup 6 found at: $innoSetupPath" "SUCCESS"
    } else {
        Handle-Error "Inno Setup 6 is not installed. Please install from https://jrsoftware.org/isinfo.php"
        throw "Inno Setup not found"
    }

    # Check project files
    Write-Host "Checking project files..." -ForegroundColor Yellow
    if (Test-Path "docker-compose.yml") {
        Write-Log "docker-compose.yml found" "SUCCESS"
    } else {
        Handle-Error "docker-compose.yml not found. Please run this script from the project root directory."
        throw "Project files not found"
    }

    if (Test-Path (Join-Path $InstallerDir "MyShopCNC-Setup.iss")) {
        Write-Log "Installer script found" "SUCCESS"
    } else {
        Handle-Error "Installer script not found at $InstallerDir\MyShopCNC-Setup.iss"
        throw "Installer script not found"
    }

    Write-Log "All prerequisites verified successfully!" "SUCCESS"
} catch {
    Write-Host ""
    Write-Host "Prerequisites check failed. Cannot continue." -ForegroundColor Red
    exit 1
}

# Step 2: Prepare Environment
Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "STEP 2: PREPARING ENVIRONMENT" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

try {
    # Create directories
    Write-Host "Creating directories..." -ForegroundColor Yellow
    if (-not (Test-Path $DockerImagesDir)) {
        New-Item -ItemType Directory -Path $DockerImagesDir | Out-Null
        Write-Log "Created $DockerImagesDir directory" "SUCCESS"
    } else {
        Write-Log "$DockerImagesDir directory exists" "SUCCESS"
    }

    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
        Write-Log "Created $OutputDir directory" "SUCCESS"
    } else {
        Write-Log "$OutputDir directory exists" "SUCCESS"
    }

    # Setup environment files
    Write-Host "Setting up environment files..." -ForegroundColor Yellow
    if (-not (Test-Path ".env") -and (Test-Path "env.dist")) {
        Copy-Item "env.dist" ".env"
        Write-Log "Created .env from env.dist" "SUCCESS"
    }

    if (-not (Test-Path "backend\.env") -and (Test-Path "backend\env.dist")) {
        Copy-Item "backend\env.dist" "backend\.env"
        Write-Log "Created backend\.env from backend\env.dist" "SUCCESS"
    }

    if (-not (Test-Path "frontend\.env") -and (Test-Path "frontend\env.dist")) {
        Copy-Item "frontend\env.dist" "frontend\.env"
        Write-Log "Created frontend\.env from frontend\env.dist" "SUCCESS"
    }
} catch {
    Handle-Error "Failed to prepare environment: $_"
    exit 1
}

# Step 3: Build Docker Images
if (-not $SkipBuild) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host "STEP 3: BUILDING DOCKER IMAGES" -ForegroundColor Yellow
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host ""

    try {
        Write-Host "Building Docker images from source..." -ForegroundColor Yellow
        Write-Host "This may take several minutes depending on your system and internet connection." -ForegroundColor Cyan
        Write-Host ""

        # Clean up existing containers
        Write-Host "Stopping and removing existing containers..." -ForegroundColor Yellow
        docker-compose down --remove-orphans 2>$null

        # Build all images
        Write-Host "Building all Docker images..." -ForegroundColor Yellow
        docker-compose build --no-cache
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Docker images built successfully" "SUCCESS"
        } else {
            Handle-Error "Failed to build Docker images"
            throw "Docker build failed"
        }
    } catch {
        Handle-Error "Docker build process failed: $_"
        exit 1
    }
} else {
    Write-Log "Skipping Docker build (--SkipBuild specified)" "WARNING"
}

# Step 4: Export Docker Images
if (-not $SkipExport) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host "STEP 4: EXPORTING DOCKER IMAGES" -ForegroundColor Yellow
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host ""

    try {
        Write-Host "Exporting Docker images to tar files..." -ForegroundColor Yellow
        Write-Host "This will create approximately 3.4GB of image files." -ForegroundColor Cyan
        Write-Host ""

        # Define images to export
        $imagesToExport = @(
            @{Name="myshopcnc-dev-frontend:latest"; File="frontend.tar"; Description="Frontend"},
            @{Name="myshopcnc-dev-nginx:latest"; File="nginx.tar"; Description="Nginx"},
            @{Name="myshopcnc-dev-dashboardservice:latest"; File="dashboardservice.tar"; Description="Dashboard Service"},
            @{Name="myshopcnc-dev-userservice:latest"; File="userservice.tar"; Description="User Service"}
        )

        # Export each image
        foreach ($image in $imagesToExport) {
            Write-Host "Exporting $($image.Description) image..." -ForegroundColor Yellow
            $outputPath = Join-Path $DockerImagesDir $image.File
            docker save $image.Name -o $outputPath
            if ($LASTEXITCODE -eq 0) {
                Write-Log "$($image.Description) image exported" "SUCCESS"
            } else {
                Handle-Error "Failed to export $($image.Description) image"
            }
        }

        # Pull and export TimescaleDB
        Write-Host "Pulling and exporting TimescaleDB image..." -ForegroundColor Yellow
        docker pull timescale/timescaledb:latest-pg15
        if ($LASTEXITCODE -eq 0) {
            $timescaleOutput = Join-Path $DockerImagesDir "timescaledb.tar"
            docker save timescale/timescaledb:latest-pg15 -o $timescaleOutput
            if ($LASTEXITCODE -eq 0) {
                Write-Log "TimescaleDB image exported" "SUCCESS"
            } else {
                Handle-Error "Failed to export TimescaleDB image"
            }
        } else {
            Handle-Error "Failed to pull TimescaleDB image"
        }

        # Verify exported files
        Write-Host ""
        Write-Host "Verifying exported images..." -ForegroundColor Yellow
        Get-ChildItem -Path $DockerImagesDir -Filter "*.tar" | ForEach-Object {
            $sizeGB = [math]::Round($_.Length / 1GB, 2)
            Write-Host "  $($_.Name): $sizeGB GB" -ForegroundColor White
        }
    } catch {
        Handle-Error "Image export process failed: $_"
        exit 1
    }
} else {
    Write-Log "Skipping image export (--SkipExport specified)" "WARNING"
}

# Step 5: Create Windows Installer
if (-not $SkipInstaller) {
    Write-Host ""
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host "STEP 5: CREATING WINDOWS INSTALLER" -ForegroundColor Yellow
    Write-Host "================================================================" -ForegroundColor Yellow
    Write-Host ""

    try {
        Write-Host "Compiling installer with Inno Setup..." -ForegroundColor Yellow
        Write-Host "This will create the final installer executable." -ForegroundColor Cyan
        Write-Host ""

        # Build installer
        Push-Location $InstallerDir
        & $innoSetupPath "MyShopCNC-Setup.iss"
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Installer built successfully" "SUCCESS"
        } else {
            Handle-Error "Failed to build installer"
            throw "Installer build failed"
        }
        Pop-Location
    } catch {
        Handle-Error "Installer creation failed: $_"
        Pop-Location
        exit 1
    }
} else {
    Write-Log "Skipping installer creation (--SkipInstaller specified)" "WARNING"
}

# Final Summary
$EndTime = Get-Date
$Duration = $EndTime - $StartTime

Write-Host ""
Write-Host "================================================================" -ForegroundColor Green
Write-Host "BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green
Write-Host ""

Write-Host "Build Summary:" -ForegroundColor Cyan
Write-Host "--------------" -ForegroundColor Cyan
Write-Host "✓ Build Duration: $($Duration.ToString('hh\:mm\:ss'))" -ForegroundColor Green
Write-Host "✓ Errors: $ErrorCount" -ForegroundColor $(if ($ErrorCount -eq 0) { "Green" } else { "Red" })
Write-Host "✓ Warnings: $WarningCount" -ForegroundColor $(if ($WarningCount -eq 0) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "Output Files:" -ForegroundColor Cyan
Write-Host "-------------" -ForegroundColor Cyan

# Check installer
$installerPath = Join-Path $OutputDir "$ProjectName-Setup-$ProjectVersion.exe"
if (Test-Path $installerPath) {
    $installerSize = [math]::Round((Get-Item $installerPath).Length / 1GB, 2)
    Write-Host "  Installer: $ProjectName-Setup-$ProjectVersion.exe ($installerSize GB)" -ForegroundColor White
    Write-Host "  Location: $OutputDir\" -ForegroundColor White
} else {
    Handle-Warning "Installer file not found in expected location"
}

Write-Host ""
Write-Host "Docker Images:" -ForegroundColor Cyan
Write-Host "--------------" -ForegroundColor Cyan
Get-ChildItem -Path $DockerImagesDir -Filter "*.tar" -ErrorAction SilentlyContinue | ForEach-Object {
    $sizeGB = [math]::Round($_.Length / 1GB, 2)
    Write-Host "  $($_.Name): $sizeGB GB" -ForegroundColor White
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "-----------" -ForegroundColor Cyan
Write-Host "1. Test the installer on a clean Windows machine" -ForegroundColor White
Write-Host "2. Distribute $ProjectName-Setup-$ProjectVersion.exe to end users" -ForegroundColor White
Write-Host "3. Users need Docker Desktop installed to run the application" -ForegroundColor White

Write-Log "Build completed successfully" "SUCCESS"
Write-Host ""
Write-Host "Build log saved to: $BuildLog" -ForegroundColor Gray
