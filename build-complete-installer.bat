@echo off
setlocal enabledelayedexpansion

title MyShopCNC - Complete Build and Installer Creation

echo ================================================================
echo MyShopCNC Complete Build and Installer Creation Script
echo ================================================================
echo.
echo This script will:
echo 1. Verify prerequisites
echo 2. Build Docker images from source
echo 3. Export Docker images to tar files
echo 4. Create Windows installer using Inno Setup
echo.

REM Set variables
set "PROJECT_NAME=MyShopCNC"
set "PROJECT_VERSION=1.0.0"
set "DOCKER_IMAGES_DIR=docker-images"
set "INSTALLER_DIR=installer"
set "OUTPUT_DIR=%INSTALLER_DIR%\output"
set "BUILD_LOG=build.log"
set "ERROR_COUNT=0"

REM Initialize log
echo Build started at %date% %time% > %BUILD_LOG%

echo ================================================================
echo STEP 1: VERIFYING PREREQUISITES
echo ================================================================
echo.

REM Check if Docker is installed and running
echo Checking Docker installation...
docker --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Docker is not installed or not in PATH
    echo Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Docker is installed
)

REM Check if Docker daemon is running
echo Checking Docker daemon...
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Docker daemon is not running
    echo Please start Docker Desktop and try again
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Docker daemon is running
)

REM Check if docker-compose is available
echo Checking Docker Compose...
docker-compose --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Docker Compose is not available
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Docker Compose is available
)

REM Check if Inno Setup is installed
echo Checking Inno Setup installation...
set "INNO_SETUP_PATH="
if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
)

if "%INNO_SETUP_PATH%"=="" (
    echo ✗ ERROR: Inno Setup 6 is not installed
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Inno Setup 6 found at: %INNO_SETUP_PATH%
)

REM Check required files
echo Checking project files...
if not exist "docker-compose.yml" (
    echo ✗ ERROR: docker-compose.yml not found
    echo Please run this script from the project root directory
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ docker-compose.yml found
)

if not exist "%INSTALLER_DIR%\MyShopCNC-Setup.iss" (
    echo ✗ ERROR: Installer script not found at %INSTALLER_DIR%\MyShopCNC-Setup.iss
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Installer script found
)

echo.
echo ✓ All prerequisites verified successfully!
echo.

echo ================================================================
echo STEP 2: PREPARING ENVIRONMENT
echo ================================================================
echo.

REM Create necessary directories
echo Creating directories...
if not exist "%DOCKER_IMAGES_DIR%" (
    mkdir "%DOCKER_IMAGES_DIR%"
    echo ✓ Created %DOCKER_IMAGES_DIR% directory
) else (
    echo ✓ %DOCKER_IMAGES_DIR% directory exists
)

if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
    echo ✓ Created %OUTPUT_DIR% directory
) else (
    echo ✓ %OUTPUT_DIR% directory exists
)

REM Setup environment files
echo Setting up environment files...
if not exist ".env" (
    if exist "env.dist" (
        copy "env.dist" ".env" >nul
        echo ✓ Created .env from env.dist
    ) else (
        echo ⚠ WARNING: No .env or env.dist file found
    )
) else (
    echo ✓ .env file exists
)

if not exist "backend\.env" (
    if exist "backend\env.dist" (
        copy "backend\env.dist" "backend\.env" >nul
        echo ✓ Created backend\.env from backend\env.dist
    )
)

if not exist "frontend\.env" (
    if exist "frontend\env.dist" (
        copy "frontend\env.dist" "frontend\.env" >nul
        echo ✓ Created frontend\.env from frontend\env.dist
    )
)

echo.
echo ================================================================
echo STEP 3: BUILDING DOCKER IMAGES
echo ================================================================
echo.

echo Building Docker images from source...
echo This may take several minutes depending on your system and internet connection.
echo.

REM Clean up any existing containers
echo Stopping and removing existing containers...
docker-compose down --remove-orphans >nul 2>&1

REM Build all images
echo Building all Docker images...
docker-compose build --no-cache
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to build Docker images
    echo Check the output above for details
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo ✓ Docker images built successfully
)

echo.
echo ================================================================
echo STEP 4: EXPORTING DOCKER IMAGES
echo ================================================================
echo.

echo Exporting Docker images to tar files...
echo This will create approximately 3.4GB of image files.
echo.

REM Get the actual image names from docker-compose
echo Identifying built images...
for /f "tokens=*" %%i in ('docker images --format "{{.Repository}}:{{.Tag}}" ^| findstr myshopcnc') do (
    echo Found image: %%i
)

REM Export frontend image
echo Exporting frontend image...
docker save myshopcnc-dev-frontend:latest -o "%DOCKER_IMAGES_DIR%\frontend.tar"
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to export frontend image
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Frontend image exported
)

REM Export nginx image
echo Exporting nginx image...
docker save myshopcnc-dev-nginx:latest -o "%DOCKER_IMAGES_DIR%\nginx.tar"
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to export nginx image
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Nginx image exported
)

REM Export dashboard service image
echo Exporting dashboard service image...
docker save myshopcnc-dev-dashboardservice:latest -o "%DOCKER_IMAGES_DIR%\dashboardservice.tar"
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to export dashboard service image
    set /a ERROR_COUNT+=1
) else (
    echo ✓ Dashboard service image exported
)

REM Export user service image
echo Exporting user service image...
docker save myshopcnc-dev-userservice:latest -o "%DOCKER_IMAGES_DIR%\userservice.tar"
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to export user service image
    set /a ERROR_COUNT+=1
) else (
    echo ✓ User service image exported
)

REM Pull and export TimescaleDB image
echo Pulling and exporting TimescaleDB image...
docker pull timescale/timescaledb:latest-pg15
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to pull TimescaleDB image
    set /a ERROR_COUNT+=1
) else (
    docker save timescale/timescaledb:latest-pg15 -o "%DOCKER_IMAGES_DIR%\timescaledb.tar"
    if %ERRORLEVEL% NEQ 0 (
        echo ✗ ERROR: Failed to export TimescaleDB image
        set /a ERROR_COUNT+=1
    ) else (
        echo ✓ TimescaleDB image exported
    )
)

REM Check exported files
echo.
echo Verifying exported images...
for %%f in ("%DOCKER_IMAGES_DIR%\*.tar") do (
    echo   %%~nxf: %%~zf bytes
)

echo.
echo ================================================================
echo STEP 5: CREATING WINDOWS INSTALLER
echo ================================================================
echo.

echo Compiling installer with Inno Setup...
echo This will create the final installer executable.
echo.

REM Change to installer directory and build
cd "%INSTALLER_DIR%"
"%INNO_SETUP_PATH%" "MyShopCNC-Setup.iss"
if %ERRORLEVEL% NEQ 0 (
    echo ✗ ERROR: Failed to build installer
    echo Check Inno Setup output for details
    set /a ERROR_COUNT+=1
    cd ..
    goto :error_exit
) else (
    echo ✓ Installer built successfully
    cd ..
)

echo.
echo ================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.

REM Display results
echo Build Summary:
echo --------------
echo ✓ Docker images built from source
echo ✓ All images exported to %DOCKER_IMAGES_DIR%\
echo ✓ Windows installer created

echo.
echo Output Files:
echo -------------
if exist "%OUTPUT_DIR%\%PROJECT_NAME%-Setup-%PROJECT_VERSION%.exe" (
    for %%f in ("%OUTPUT_DIR%\%PROJECT_NAME%-Setup-%PROJECT_VERSION%.exe") do (
        echo   Installer: %%~nxf (%%~zf bytes)
    )
    echo   Location: %OUTPUT_DIR%\
) else (
    echo   ⚠ WARNING: Installer file not found in expected location
)

echo.
echo Docker Images:
echo --------------
for %%f in ("%DOCKER_IMAGES_DIR%\*.tar") do (
    echo   %%~nxf: %%~zf bytes
)

echo.
echo Next Steps:
echo -----------
echo 1. Test the installer on a clean Windows machine
echo 2. Distribute %PROJECT_NAME%-Setup-%PROJECT_VERSION%.exe to end users
echo 3. Users need Docker Desktop installed to run the application
echo.
echo The installer includes:
echo - Complete application stack
echo - All Docker images (3.4GB)
echo - Management scripts and shortcuts
echo - Installation validation tools
echo.

goto :end

:error_exit
echo.
echo ================================================================
echo BUILD FAILED!
echo ================================================================
echo.
echo Found %ERROR_COUNT% error(s). Please resolve the issues above and try again.
echo.
echo Common solutions:
echo - Ensure Docker Desktop is installed and running
echo - Install Inno Setup 6 from https://jrsoftware.org/isinfo.php
echo - Run this script from the project root directory
echo - Check that all required files exist
echo.
echo Build log saved to: %BUILD_LOG%
echo.
exit /b 1

:end
echo Build completed at %date% %time% >> %BUILD_LOG%
echo.
echo Build log saved to: %BUILD_LOG%
echo.
pause
