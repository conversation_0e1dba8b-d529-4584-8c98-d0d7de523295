# PowerShell script to load Docker images for MyShopCNC
# This script loads all the Docker images required for the application

Write-Host "Loading MyShopCNC Docker Images..." -ForegroundColor Green

# Get the installation directory
$InstallDir = $env:MYSHOPCNC_INSTALL_DIR
if (-not $InstallDir) {
    $InstallDir = "${env:ProgramFiles}\MyShopCNC"
}

$ImagesDir = Join-Path $InstallDir "docker-images"

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "Docker is running." -ForegroundColor Green
} catch {
    Write-Host "Docker is not running or not installed. Please start Docker Desktop." -ForegroundColor Red
    Read-Host "Press Enter to continue after starting Docker Desktop"
}

# Load each Docker image
$images = @(
    @{Name="Frontend"; File="frontend.tar"},
    @{Name="Nginx"; File="nginx.tar"},
    @{Name="Dashboard Service"; File="dashboardservice.tar"},
    @{Name="User Service"; File="userservice.tar"},
    @{Name="TimescaleDB"; File="timescaledb.tar"}
)

foreach ($image in $images) {
    $imagePath = Join-Path $ImagesDir $image.File
    if (Test-Path $imagePath) {
        Write-Host "Loading $($image.Name)..." -ForegroundColor Yellow
        try {
            docker load -i $imagePath
            Write-Host "$($image.Name) loaded successfully." -ForegroundColor Green
        } catch {
            Write-Host "Failed to load $($image.Name): $_" -ForegroundColor Red
        }
    } else {
        Write-Host "Image file not found: $imagePath" -ForegroundColor Red
    }
}

Write-Host "Docker image loading completed." -ForegroundColor Green
Write-Host "You can now start MyShopCNC using the desktop shortcut or start menu." -ForegroundColor Cyan
