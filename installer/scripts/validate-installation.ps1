# PowerShell script to validate MyShopCNC installation
# This script checks if all components are properly installed and configured

Write-Host "MyShopCNC Installation Validator" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

$InstallDir = $env:MYSHOPCNC_INSTALL_DIR
if (-not $InstallDir) {
    $InstallDir = "${env:ProgramFiles}\MyShopCNC"
}

$ValidationResults = @()

# Function to add validation result
function Add-ValidationResult {
    param($Component, $Status, $Message)
    $ValidationResults += [PSCustomObject]@{
        Component = $Component
        Status = $Status
        Message = $Message
    }
}

# Check installation directory
Write-Host "Checking installation directory..." -ForegroundColor Yellow
if (Test-Path $InstallDir) {
    Add-ValidationResult "Installation Directory" "✓ PASS" "Found at: $InstallDir"
} else {
    Add-ValidationResult "Installation Directory" "✗ FAIL" "Not found at: $InstallDir"
}

# Check Docker installation
Write-Host "Checking Docker installation..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($dockerVersion) {
        Add-ValidationResult "Docker Desktop" "✓ PASS" $dockerVersion
    } else {
        Add-ValidationResult "Docker Desktop" "✗ FAIL" "Docker command not found"
    }
} catch {
    Add-ValidationResult "Docker Desktop" "✗ FAIL" "Docker not accessible"
}

# Check Docker daemon
Write-Host "Checking Docker daemon..." -ForegroundColor Yellow
try {
    docker info 2>$null | Out-Null
    Add-ValidationResult "Docker Daemon" "✓ PASS" "Docker daemon is running"
} catch {
    Add-ValidationResult "Docker Daemon" "⚠ WARN" "Docker daemon not running"
}

# Check application files
Write-Host "Checking application files..." -ForegroundColor Yellow
$requiredFiles = @(
    "app\docker-compose.yml",
    "app\.env",
    "app\frontend\.env",
    "app\backend\.env"
)

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $InstallDir $file
    if (Test-Path $filePath) {
        Add-ValidationResult "App File: $file" "✓ PASS" "File exists"
    } else {
        Add-ValidationResult "App File: $file" "✗ FAIL" "File missing"
    }
}

# Check Docker images
Write-Host "Checking Docker images..." -ForegroundColor Yellow
$requiredImages = @(
    "myshopcnc-dev-frontend",
    "myshopcnc-dev-nginx", 
    "myshopcnc-dev-dashboardservice",
    "myshopcnc-dev-userservice",
    "timescale/timescaledb"
)

foreach ($image in $requiredImages) {
    try {
        $imageExists = docker images --format "table {{.Repository}}:{{.Tag}}" | Select-String $image
        if ($imageExists) {
            Add-ValidationResult "Docker Image: $image" "✓ PASS" "Image loaded"
        } else {
            Add-ValidationResult "Docker Image: $image" "✗ FAIL" "Image not found"
        }
    } catch {
        Add-ValidationResult "Docker Image: $image" "✗ FAIL" "Cannot check image"
    }
}

# Check scripts
Write-Host "Checking scripts..." -ForegroundColor Yellow
$requiredScripts = @(
    "scripts\start-myshopcnc.bat",
    "scripts\stop-myshopcnc.bat",
    "scripts\load-docker-images.bat",
    "scripts\start-myshopcnc.ps1",
    "scripts\stop-myshopcnc.ps1",
    "scripts\load-docker-images.ps1"
)

foreach ($script in $requiredScripts) {
    $scriptPath = Join-Path $InstallDir $script
    if (Test-Path $scriptPath) {
        Add-ValidationResult "Script: $script" "✓ PASS" "Script exists"
    } else {
        Add-ValidationResult "Script: $script" "✗ FAIL" "Script missing"
    }
}

# Display results
Write-Host ""
Write-Host "Validation Results:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host ""

$passCount = 0
$failCount = 0
$warnCount = 0

foreach ($result in $ValidationResults) {
    $color = "White"
    if ($result.Status.Contains("PASS")) { 
        $color = "Green"
        $passCount++
    } elseif ($result.Status.Contains("FAIL")) { 
        $color = "Red"
        $failCount++
    } elseif ($result.Status.Contains("WARN")) { 
        $color = "Yellow"
        $warnCount++
    }
    
    Write-Host ("{0,-30} {1,-10} {2}" -f $result.Component, $result.Status, $result.Message) -ForegroundColor $color
}

Write-Host ""
Write-Host "Summary:" -ForegroundColor Cyan
Write-Host "  Passed: $passCount" -ForegroundColor Green
Write-Host "  Failed: $failCount" -ForegroundColor Red
Write-Host "  Warnings: $warnCount" -ForegroundColor Yellow

if ($failCount -eq 0) {
    Write-Host ""
    Write-Host "✓ Installation validation completed successfully!" -ForegroundColor Green
    Write-Host "MyShopCNC should be ready to use." -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "✗ Installation validation found issues!" -ForegroundColor Red
    Write-Host "Please resolve the failed items before using MyShopCNC." -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
