# PowerShell script to start MyShopCNC application
# This script starts all the Docker containers for the application

Write-Host "Starting MyShopCNC Application..." -ForegroundColor Green

# Get the installation directory
$InstallDir = $env:MYSHOPCNC_INSTALL_DIR
if (-not $InstallDir) {
    $InstallDir = "${env:ProgramFiles}\MyShopCNC"
}

$AppDir = Join-Path $InstallDir "app"
$DockerComposePath = Join-Path $AppDir "docker-compose.yml"

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "Docker is running." -ForegroundColor Green
} catch {
    Write-Host "Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Change to application directory
Set-Location $AppDir

# Check if docker-compose.yml exists
if (-not (Test-Path $DockerComposePath)) {
    Write-Host "docker-compose.yml not found at: $DockerComposePath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Start the application
Write-Host "Starting MyShopCNC containers..." -ForegroundColor Yellow
try {
    docker-compose up -d
    Write-Host "MyShopCNC started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Application URLs:" -ForegroundColor Cyan
    Write-Host "  Frontend: http://localhost:8080" -ForegroundColor White
    Write-Host "  API Gateway: http://localhost" -ForegroundColor White
    Write-Host ""
    Write-Host "To stop the application, use the 'Stop MyShopCNC' shortcut or run:" -ForegroundColor Yellow
    Write-Host "  docker-compose down" -ForegroundColor White
    Write-Host ""
    
    # Ask if user wants to open the application in browser
    $openBrowser = Read-Host "Would you like to open the application in your browser? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "http://localhost:8080"
    }
} catch {
    Write-Host "Failed to start MyShopCNC: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "Press Enter to exit"
