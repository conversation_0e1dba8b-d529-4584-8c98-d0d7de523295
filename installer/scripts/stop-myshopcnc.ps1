# PowerShell script to stop MyShopCNC application
# This script stops all the Docker containers for the application

Write-Host "Stopping MyShopCNC Application..." -ForegroundColor Yellow

# Get the installation directory
$InstallDir = $env:MYSHOPCNC_INSTALL_DIR
if (-not $InstallDir) {
    $InstallDir = "${env:ProgramFiles}\MyShopCNC"
}

$AppDir = Join-Path $InstallDir "app"
$DockerComposePath = Join-Path $AppDir "docker-compose.yml"

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "Docker is running." -ForegroundColor Green
} catch {
    Write-Host "Docker is not running. Application may already be stopped." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 0
}

# Change to application directory
Set-Location $AppDir

# Check if docker-compose.yml exists
if (-not (Test-Path $DockerComposePath)) {
    Write-Host "docker-compose.yml not found at: $DockerComposePath" -ForegroundColor Red
    Write-Host "Application may not be installed correctly." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Stop the application
Write-Host "Stopping MyShopCNC containers..." -ForegroundColor Yellow
try {
    docker-compose down
    Write-Host "MyShopCNC stopped successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to stop MyShopCNC: $_" -ForegroundColor Red
    Write-Host "You may need to stop the containers manually using Docker Desktop." -ForegroundColor Yellow
}

Read-Host "Press Enter to exit"
