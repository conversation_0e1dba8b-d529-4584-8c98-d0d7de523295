@echo off
title MyShopCNC - Load Docker Images

echo Loading MyShopCNC Docker Images...
echo This may take several minutes depending on your system.
echo.

REM Set the installation directory
if "%MYSHOPCNC_INSTALL_DIR%"=="" (
    set "MYSHOPCNC_INSTALL_DIR=%ProgramFiles%\MyShopCNC"
)

REM Run the PowerShell script
powershell.exe -ExecutionPolicy Bypass -File "%MYSHOPCNC_INSTALL_DIR%\scripts\load-docker-images.ps1"

pause
