# MyShopCNC Windows Installer

This directory contains the Inno Setup script and supporting files to create a Windows installer for MyShopCNC.

## Prerequisites

1. **Inno Setup 6.0 or later** - Download from https://jrsoftware.org/isinfo.php
2. **Docker Desktop for Windows** - The installer will check for this and prompt to install if missing

## Building the Installer

1. Ensure all Docker images are exported to the `../docker-images/` directory
2. Open `MyShopCNC-Setup.iss` in Inno Setup Compiler
3. Click "Build" or press F9 to compile the installer
4. The installer will be created in the `output/` directory

## What the Installer Does

The installer will:

1. **Check for Docker Desktop** - Prompts user to install if not found
2. **Install Application Files** - Copies all necessary files to Program Files
3. **Install Docker Images** - Includes all container images (3.4GB total)
4. **Create Shortcuts** - Desktop and Start Menu shortcuts for easy access
5. **Set Environment Variables** - Sets MYSHOPCNC_INSTALL_DIR for scripts
6. **Load Docker Images** - Optionally loads images into Docker after installation
7. **Start Application** - Optionally starts the application after installation

## Installer Contents

### Application Files
- `docker-compose.yml` - Main orchestration file
- Environment files (`.env`) for all services
- Documentation (`README.md`)

### Docker Images (3.4GB total)
- `frontend.tar` (59MB) - Angular frontend with Nginx
- `nginx.tar` (188MB) - Reverse proxy
- `dashboardservice.tar` (1.1GB) - Node.js dashboard service
- `userservice.tar` (1.1GB) - Node.js user service  
- `timescaledb.tar` (1.1GB) - TimescaleDB database

### Scripts
- `start-myshopcnc.bat/.ps1` - Start the application
- `stop-myshopcnc.bat/.ps1` - Stop the application
- `load-docker-images.bat/.ps1` - Load Docker images

## User Experience

After installation, users can:

1. **Start MyShopCNC** - Use desktop shortcut or Start Menu
2. **Access Application** - Opens at http://localhost:8080
3. **Stop Application** - Use Stop shortcut or docker-compose down
4. **Manage Images** - Reload images if needed

## System Requirements

- Windows 10 version 1809 or later (64-bit)
- Docker Desktop for Windows
- 8GB RAM minimum (16GB recommended)
- 10GB free disk space (for images and containers)
- Administrator privileges for installation

## Troubleshooting

If Docker images fail to load:
1. Ensure Docker Desktop is running
2. Use "Load Docker Images" shortcut to retry
3. Check Docker Desktop for any issues

If application fails to start:
1. Verify Docker Desktop is running
2. Check that all images are loaded: `docker images`
3. Review logs: `docker-compose logs`

## Customization

To customize the installer:
1. Edit `MyShopCNC-Setup.iss` for installer settings
2. Replace `icon.ico` with your application icon
3. Update `license.txt` with your license terms
4. Modify PowerShell scripts for different behavior

## File Structure

```
installer/
├── MyShopCNC-Setup.iss     # Main Inno Setup script
├── license.txt             # License agreement
├── icon.ico               # Application icon (add your own)
├── scripts/               # Installation scripts
│   ├── *.ps1             # PowerShell scripts
│   └── *.bat             # Batch file wrappers
└── output/               # Generated installer output
```
