; MyShopCNC Installer Script for Inno Setup
; This script creates a Windows installer for the MyShopCNC application
; including Docker images and all necessary components

#define MyAppName "MyShopCNC"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "MyShopCNC Solutions"
#define MyAppURL "https://myshopcnc.com"
#define MyAppExeName "start-myshopcnc.bat"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
LicenseFile=license.txt
OutputDir=output
OutputBaseFilename=MyShopCNC-Setup-{#MyAppVersion}
;SetupIconFile=icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64
MinVersion=10.0.17763

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
; Application files
Source: "..\docker-compose.yml"; DestDir: "{app}\app"; Flags: ignoreversion
Source: "..\frontend\.env"; DestDir: "{app}\app\frontend"; Flags: ignoreversion
Source: "..\backend\.env"; DestDir: "{app}\app\backend"; Flags: ignoreversion
Source: "..\.env"; DestDir: "{app}\app"; Flags: ignoreversion

; Docker images
Source: "..\docker-images\*.tar"; DestDir: "{app}\docker-images"; Flags: ignoreversion

; Scripts
Source: "scripts\*.ps1"; DestDir: "{app}\scripts"; Flags: ignoreversion
Source: "scripts\*.bat"; DestDir: "{app}\scripts"; Flags: ignoreversion

; Documentation
Source: "..\README.md"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Start {#MyAppName}"; Filename: "{app}\scripts\start-myshopcnc.bat"; WorkingDir: "{app}"
Name: "{group}\Stop {#MyAppName}"; Filename: "{app}\scripts\stop-myshopcnc.bat"; WorkingDir: "{app}"
Name: "{group}\Load Docker Images"; Filename: "{app}\scripts\load-docker-images.bat"; WorkingDir: "{app}"
Name: "{group}\Validate Installation"; Filename: "{app}\scripts\validate-installation.bat"; WorkingDir: "{app}"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Start {#MyAppName}"; Filename: "{app}\scripts\start-myshopcnc.bat"; WorkingDir: "{app}"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\scripts\start-myshopcnc.bat"; WorkingDir: "{app}"; Tasks: quicklaunchicon

[Registry]
Root: HKLM; Subkey: "SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; ValueType: string; ValueName: "MYSHOPCNC_INSTALL_DIR"; ValueData: "{app}"; Flags: preservestringtype

[Run]
Filename: "{app}\scripts\load-docker-images.bat"; Description: "Load Docker images for MyShopCNC"; Flags: postinstall runascurrentuser
Filename: "{app}\scripts\start-myshopcnc.bat"; Description: "Start MyShopCNC application"; Flags: postinstall nowait runascurrentuser

[UninstallRun]
Filename: "{app}\scripts\stop-myshopcnc.bat"; RunOnceId: "StopMyShopCNC"

[Code]
function InitializeSetup(): Boolean;
var
  ResultCode: Integer;
begin
  // Check if Docker Desktop is installed
  if not Exec('docker', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
  begin
    if MsgBox('Docker Desktop is not installed or not in PATH. MyShopCNC requires Docker Desktop to run.' + #13#10 + 
              'Would you like to download and install Docker Desktop first?', 
              mbConfirmation, MB_YESNO) = IDYES then
    begin
      ShellExec('open', 'https://www.docker.com/products/docker-desktop/', '', '', SW_SHOWNORMAL, ewNoWait, ResultCode);
      MsgBox('Please install Docker Desktop and restart this installer.', mbInformation, MB_OK);
      Result := False;
    end
    else
    begin
      Result := False;
    end;
  end
  else
  begin
    Result := True;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // Set environment variable for current session
    SetEnvironmentVariable('MYSHOPCNC_INSTALL_DIR', ExpandConstant('{app}'));
  end;
end;
