@echo off
title MyShopCNC - Build Installer

echo Building MyShopCNC Windows Installer...
echo.

REM Check if Inno Setup is installed
set "INNO_SETUP_PATH=%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    set "INNO_SETUP_PATH=%ProgramFiles%\Inno Setup 6\ISCC.exe"
)

if not exist "%INNO_SETUP_PATH%" (
    echo ERROR: Inno Setup 6 not found!
    echo Please install Inno Setup 6 from: https://jrsoftware.org/isinfo.php
    echo.
    pause
    exit /b 1
)

REM Check if Docker images exist
if not exist "..\docker-images\frontend.tar" (
    echo ERROR: Docker images not found!
    echo Please run the Docker build process first to create the images.
    echo Expected location: ..\docker-images\*.tar
    echo.
    pause
    exit /b 1
)

REM Create output directory
if not exist "output" mkdir output

REM Build the installer
echo Compiling installer with Inno Setup...
"%INNO_SETUP_PATH%" "MyShopCNC-Setup.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Installer built successfully!
    echo Output location: output\MyShopCNC-Setup-1.0.0.exe
    echo.
    echo Installer size will be approximately 3.4GB due to included Docker images.
    echo.
) else (
    echo.
    echo ERROR: Failed to build installer!
    echo Check the Inno Setup output for details.
    echo.
)

pause
