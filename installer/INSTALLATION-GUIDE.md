# MyShopCNC Windows Installation Guide

This guide walks you through building and using the MyShopCNC Windows installer.

## Prerequisites

### For Building the Installer
1. **Docker Desktop** - Must be installed and running
2. **Inno Setup 6.0+** - Download from https://jrsoftware.org/isinfo.php
3. **Windows 10/11** - 64-bit version required
4. **8GB+ RAM** - For building Docker images
5. **10GB+ free space** - For images and installer

### For End Users
1. **Windows 10 1809+** - 64-bit version required
2. **Docker Desktop** - Will be prompted to install if missing
3. **8GB+ RAM** - Recommended for running containers
4. **10GB+ free space** - For application and images

## Building the Installer

### Step 1: Build Docker Images
```bash
# From the project root directory
docker-compose build
```

### Step 2: Export Docker Images
```bash
# Create images directory
mkdir docker-images

# Export all images
docker save myshopcnc-dev-frontend:latest -o docker-images/frontend.tar
docker save myshopcnc-dev-nginx:latest -o docker-images/nginx.tar
docker save myshopcnc-dev-dashboardservice:latest -o docker-images/dashboardservice.tar
docker save myshopcnc-dev-userservice:latest -o docker-images/userservice.tar
docker save timescale/timescaledb:latest-pg15 -o docker-images/timescaledb.tar
```

### Step 3: Build the Installer
```bash
# From the installer directory
cd installer
build-installer.bat
```

Or manually:
1. Open `MyShopCNC-Setup.iss` in Inno Setup Compiler
2. Click "Build" or press F9
3. Installer will be created in `output/` directory

## Installation Process

### For End Users

1. **Download** the `MyShopCNC-Setup-1.0.0.exe` installer
2. **Run as Administrator** (required for installation)
3. **Follow the wizard**:
   - Accept license agreement
   - Choose installation directory (default: `C:\Program Files\MyShopCNC`)
   - Select additional tasks (desktop shortcuts, etc.)
4. **Docker Check**: Installer will check for Docker Desktop
   - If not found, you'll be prompted to install it first
   - Download from: https://www.docker.com/products/docker-desktop/
5. **Installation**: Files will be copied (may take several minutes due to 3.4GB of images)
6. **Post-Install Options**:
   - Load Docker images (recommended)
   - Start MyShopCNC application

## Using MyShopCNC

### Starting the Application
- **Desktop Shortcut**: Double-click "Start MyShopCNC"
- **Start Menu**: MyShopCNC → Start MyShopCNC
- **Manual**: Run `start-myshopcnc.bat` from installation directory

### Accessing the Application
- **Frontend**: http://localhost:8080
- **API Gateway**: http://localhost

### Stopping the Application
- **Start Menu**: MyShopCNC → Stop MyShopCNC
- **Manual**: Run `stop-myshopcnc.bat` from installation directory
- **Command Line**: `docker-compose down` from app directory

## Troubleshooting

### Docker Issues
**Problem**: "Docker is not running"
**Solution**: 
1. Start Docker Desktop
2. Wait for it to fully initialize
3. Try starting MyShopCNC again

**Problem**: "Docker images not found"
**Solution**:
1. Use "Load Docker Images" shortcut
2. Or run `load-docker-images.bat` manually

### Application Issues
**Problem**: "Port already in use"
**Solution**:
1. Stop any other web servers on ports 80 or 8080
2. Or modify `docker-compose.yml` to use different ports

**Problem**: "Application won't start"
**Solution**:
1. Run validation: `validate-installation.bat`
2. Check Docker Desktop is running
3. Verify all images are loaded: `docker images`

### Performance Issues
**Problem**: "Application is slow"
**Solution**:
1. Increase Docker Desktop memory allocation (8GB+)
2. Close unnecessary applications
3. Ensure SSD storage for better I/O

## File Locations

### Installation Directory
```
C:\Program Files\MyShopCNC\
├── app\                    # Application files
│   ├── docker-compose.yml
│   ├── .env
│   ├── frontend\.env
│   └── backend\.env
├── docker-images\          # Docker image files (3.4GB)
│   ├── frontend.tar
│   ├── nginx.tar
│   ├── dashboardservice.tar
│   ├── userservice.tar
│   └── timescaledb.tar
├── scripts\               # Management scripts
│   ├── start-myshopcnc.bat
│   ├── stop-myshopcnc.bat
│   ├── load-docker-images.bat
│   └── validate-installation.bat
└── README.md
```

### Shortcuts Created
- **Desktop**: Start MyShopCNC
- **Start Menu**: MyShopCNC folder with all shortcuts
- **Quick Launch**: MyShopCNC (Windows 7 only)

## Uninstallation

1. **Stop Application**: Use "Stop MyShopCNC" shortcut first
2. **Uninstall**: Use "Uninstall MyShopCNC" from Start Menu
3. **Clean Docker**: Optionally remove Docker images:
   ```bash
   docker rmi myshopcnc-dev-frontend myshopcnc-dev-nginx myshopcnc-dev-dashboardservice myshopcnc-dev-userservice timescale/timescaledb:latest-pg15
   ```

## Advanced Configuration

### Changing Ports
Edit `C:\Program Files\MyShopCNC\app\docker-compose.yml`:
```yaml
services:
  frontend:
    ports:
      - '8080:80'  # Change 8080 to desired port
  nginx:
    ports:
      - '80:80'    # Change 80 to desired port
```

### Environment Variables
Edit the `.env` files in the app directory to modify database settings, API endpoints, etc.

### Custom Docker Settings
Modify `docker-compose.yml` for:
- Memory limits
- Volume mounts
- Network settings
- Environment variables

## Support

For issues with:
- **Installation**: Check this guide and run validation script
- **Docker**: Visit Docker Desktop documentation
- **Application**: Check application logs with `docker-compose logs`

## Version Information

- **MyShopCNC Version**: 1.0.0
- **Docker Images**: ~3.4GB total
- **Installer Size**: ~3.4GB
- **Minimum Windows**: 10 version 1809
- **Architecture**: x64 only
