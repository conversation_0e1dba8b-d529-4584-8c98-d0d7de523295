@echo off
title MyShopCNC - Test Build Scripts

echo MyShopCNC Build Scripts Test
echo =============================
echo.
echo This script tests the build scripts without actually building anything.
echo It verifies that all prerequisites are met and scripts are properly configured.
echo.

set "ERRORS=0"
set "WARNINGS=0"

echo Testing build script prerequisites...
echo.

REM Test 1: Check if main build scripts exist
echo [TEST 1] Checking build script files...
if exist "build-installer.bat" (
    echo ✓ build-installer.bat found
) else (
    echo ✗ build-installer.bat missing
    set /a ERRORS+=1
)

if exist "build-complete-installer.bat" (
    echo ✓ build-complete-installer.bat found
) else (
    echo ✗ build-complete-installer.bat missing
    set /a ERRORS+=1
)

if exist "build-complete-installer.ps1" (
    echo ✓ build-complete-installer.ps1 found
) else (
    echo ✗ build-complete-installer.ps1 missing
    set /a ERRORS+=1
)

echo.

REM Test 2: Check project structure
echo [TEST 2] Checking project structure...
if exist "docker-compose.yml" (
    echo ✓ docker-compose.yml found
) else (
    echo ✗ docker-compose.yml missing
    set /a ERRORS+=1
)

if exist "frontend" (
    echo ✓ frontend directory found
) else (
    echo ✗ frontend directory missing
    set /a ERRORS+=1
)

if exist "backend" (
    echo ✓ backend directory found
) else (
    echo ✗ backend directory missing
    set /a ERRORS+=1
)

if exist "installer\MyShopCNC-Setup.iss" (
    echo ✓ Inno Setup script found
) else (
    echo ✗ Inno Setup script missing
    set /a ERRORS+=1
)

echo.

REM Test 3: Check Docker
echo [TEST 3] Checking Docker installation...
docker --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Docker is installed
    
    REM Check if Docker daemon is running
    docker info >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Docker daemon is running
    ) else (
        echo ⚠ Docker daemon is not running
        echo   Please start Docker Desktop before building
        set /a WARNINGS+=1
    )
) else (
    echo ✗ Docker is not installed or not in PATH
    echo   Please install Docker Desktop from: https://www.docker.com/products/docker-desktop/
    set /a ERRORS+=1
)

echo.

REM Test 4: Check Docker Compose
echo [TEST 4] Checking Docker Compose...
docker-compose --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Docker Compose is available
) else (
    echo ✗ Docker Compose is not available
    set /a ERRORS+=1
)

echo.

REM Test 5: Check Inno Setup
echo [TEST 5] Checking Inno Setup installation...
set "INNO_FOUND=0"
if exist "%ProgramFiles(x86)%\Inno Setup 6\ISCC.exe" (
    echo ✓ Inno Setup 6 found at: %ProgramFiles(x86)%\Inno Setup 6\ISCC.exe
    set "INNO_FOUND=1"
) else if exist "%ProgramFiles%\Inno Setup 6\ISCC.exe" (
    echo ✓ Inno Setup 6 found at: %ProgramFiles%\Inno Setup 6\ISCC.exe
    set "INNO_FOUND=1"
) else (
    echo ✗ Inno Setup 6 not found
    echo   Please install from: https://jrsoftware.org/isinfo.php
    set /a ERRORS+=1
)

echo.

REM Test 6: Check PowerShell execution policy
echo [TEST 6] Checking PowerShell execution policy...
for /f "tokens=*" %%i in ('powershell -Command "Get-ExecutionPolicy"') do set "EXEC_POLICY=%%i"
if "%EXEC_POLICY%"=="Restricted" (
    echo ⚠ PowerShell execution policy is Restricted
    echo   PowerShell script may not run without -ExecutionPolicy Bypass
    set /a WARNINGS+=1
) else (
    echo ✓ PowerShell execution policy: %EXEC_POLICY%
)

echo.

REM Test 7: Check disk space
echo [TEST 7] Checking available disk space...
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do set "FREE_SPACE=%%i"
REM Remove commas from the number
set "FREE_SPACE=%FREE_SPACE:,=%"
REM Convert to GB (approximate)
set /a "FREE_GB=%FREE_SPACE:~0,-9%"
if %FREE_GB% GEQ 15 (
    echo ✓ Sufficient disk space: %FREE_GB%GB available
) else (
    echo ⚠ Low disk space: %FREE_GB%GB available
    echo   Recommended: 15GB+ for building and installer creation
    set /a WARNINGS+=1
)

echo.

REM Test 8: Test script syntax
echo [TEST 8] Testing script syntax...
echo Testing batch script syntax...
call :test_batch_syntax
if %ERRORLEVEL% EQU 0 (
    echo ✓ Batch script syntax OK
) else (
    echo ✗ Batch script syntax error
    set /a ERRORS+=1
)

echo Testing PowerShell script syntax...
powershell -Command "try { . '.\build-complete-installer.ps1' -WhatIf 2>$null } catch { exit 1 }" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ PowerShell script syntax OK
) else (
    echo ⚠ PowerShell script syntax check failed (may be normal)
    set /a WARNINGS+=1
)

echo.

REM Summary
echo =============================
echo TEST SUMMARY
echo =============================
echo.
if %ERRORS% EQU 0 (
    echo ✓ ALL TESTS PASSED!
    echo.
    echo Your system is ready to build the MyShopCNC installer.
    echo.
    echo To start building:
    echo   1. Run: build-installer.bat
    echo   2. Or run: build-complete-installer.bat
    echo   3. Or run: powershell -ExecutionPolicy Bypass -File build-complete-installer.ps1
    echo.
    if %WARNINGS% GTR 0 (
        echo Note: %WARNINGS% warning(s) found - check above for details.
    )
) else (
    echo ✗ TESTS FAILED!
    echo.
    echo Found %ERRORS% error(s) and %WARNINGS% warning(s).
    echo Please resolve the errors above before attempting to build.
    echo.
    echo Common solutions:
    echo - Install Docker Desktop and start it
    echo - Install Inno Setup 6
    echo - Ensure you're in the project root directory
    echo - Free up disk space (15GB+ recommended)
)

echo.
pause
goto :eof

:test_batch_syntax
REM Simple syntax test for batch file
findstr /C:"goto :error_exit" build-complete-installer.bat >nul
if %ERRORLEVEL% NEQ 0 exit /b 1
findstr /C:"echo ✓" build-complete-installer.bat >nul
if %ERRORLEVEL% NEQ 0 exit /b 1
exit /b 0
