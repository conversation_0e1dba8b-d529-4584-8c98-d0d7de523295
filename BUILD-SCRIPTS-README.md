# MyShopCNC Build Scripts

This directory contains automated build scripts that handle the complete process from source code to Windows installer.

## 🚀 Quick Start

### Simple Build (Recommended)
```bash
# Run the main build script and choose your preferred method
build-installer.bat
```

### Direct Execution
```bash
# Batch script (simple, works everywhere)
build-complete-installer.bat

# PowerShell script (advanced features)
powershell -ExecutionPolicy Bypass -File build-complete-installer.ps1
```

## 📋 What These Scripts Do

The build scripts automate the entire process:

1. **✅ Verify Prerequisites**
   - Check Docker Desktop installation and status
   - Verify Docker Compose availability
   - Confirm Inno Setup 6 installation
   - Validate project files and structure

2. **🔧 Prepare Environment**
   - Create necessary directories
   - Set up environment files from templates
   - Initialize build logging

3. **🐳 Build Docker Images**
   - Clean existing containers
   - Build all services from source:
     - Angular frontend with Nginx
     - Node.js dashboard service
     - Node.js user service
     - Nginx reverse proxy
   - Pull TimescaleDB image

4. **💾 Export Docker Images**
   - Save all images to tar files
   - Create ~3.4GB of image files:
     - `frontend.tar` (59MB)
     - `nginx.tar` (188MB)
     - `dashboardservice.tar` (1.1GB)
     - `userservice.tar` (1.1GB)
     - `timescaledb.tar` (1.1GB)

5. **📦 Create Windows Installer**
   - Compile Inno Setup script
   - Package all images and scripts
   - Generate `MyShopCNC-Setup-1.0.0.exe`

## 🛠️ Available Scripts

### `build-installer.bat` (Main Entry Point)
Interactive script that lets you choose between batch or PowerShell execution.

**Features:**
- User-friendly menu interface
- Choice between batch and PowerShell scripts
- Quick build option
- Parameter input for PowerShell script

### `build-complete-installer.bat` (Batch Version)
Complete automation using Windows batch scripting.

**Features:**
- ✅ Works on all Windows systems
- ✅ No PowerShell dependencies
- ✅ Simple progress reporting
- ✅ Error detection and reporting
- ✅ Comprehensive logging

**Usage:**
```bash
build-complete-installer.bat
```

### `build-complete-installer.ps1` (PowerShell Version)
Advanced automation with PowerShell features.

**Features:**
- ✅ Advanced error handling
- ✅ Detailed progress reporting
- ✅ Colored output
- ✅ Command-line parameters
- ✅ Comprehensive logging
- ✅ Build time tracking

**Usage:**
```powershell
# Basic usage
.\build-complete-installer.ps1

# Skip Docker build (use existing images)
.\build-complete-installer.ps1 -SkipBuild

# Skip image export (use existing tar files)
.\build-complete-installer.ps1 -SkipExport

# Skip installer creation (just build and export)
.\build-complete-installer.ps1 -SkipInstaller

# Verbose output
.\build-complete-installer.ps1 -Verbose

# Combine parameters
.\build-complete-installer.ps1 -SkipBuild -SkipExport
```

## 📋 Prerequisites

### Required Software
1. **Docker Desktop for Windows**
   - Download: https://www.docker.com/products/docker-desktop/
   - Must be running before executing scripts

2. **Inno Setup 6.0 or later**
   - Download: https://jrsoftware.org/isinfo.php
   - Required for creating Windows installer

### System Requirements
- **Windows 10/11** (64-bit)
- **8GB RAM** minimum (16GB recommended for building)
- **15GB free disk space** (for images, build cache, and installer)
- **Internet connection** (for pulling base images and dependencies)

### Project Requirements
- Must be run from project root directory
- `docker-compose.yml` must exist
- `installer/MyShopCNC-Setup.iss` must exist

## 📊 Build Process Timeline

Typical build times on a modern system:

| Step | Duration | Description |
|------|----------|-------------|
| Prerequisites Check | 10 seconds | Verify software and files |
| Environment Setup | 5 seconds | Create directories and files |
| Docker Build | 5-15 minutes | Build all images from source |
| Image Export | 2-5 minutes | Save 3.4GB of images to disk |
| Installer Creation | 30-60 seconds | Compile Inno Setup script |
| **Total** | **8-21 minutes** | Complete process |

*Times vary based on system performance, internet speed, and Docker cache status.*

## 📁 Output Files

### Generated Directory Structure
```
MyShopCNC-dev/
├── docker-images/           # Exported Docker images (3.4GB)
│   ├── frontend.tar
│   ├── nginx.tar
│   ├── dashboardservice.tar
│   ├── userservice.tar
│   └── timescaledb.tar
├── installer/
│   └── output/
│       └── MyShopCNC-Setup-1.0.0.exe  # Final installer (~3.4GB)
└── build.log               # Build process log
```

### Installer Features
The generated installer includes:
- ✅ Docker Desktop dependency check
- ✅ All Docker images (3.4GB) embedded
- ✅ Management scripts and shortcuts
- ✅ Installation validation tools
- ✅ Start Menu and desktop shortcuts
- ✅ Clean uninstall process

## 🔧 Troubleshooting

### Common Issues

**"Docker is not installed or not in PATH"**
- Install Docker Desktop from https://www.docker.com/products/docker-desktop/
- Restart command prompt after installation

**"Docker daemon is not running"**
- Start Docker Desktop
- Wait for it to fully initialize (green icon in system tray)

**"Inno Setup 6 is not installed"**
- Download and install from https://jrsoftware.org/isinfo.php
- Ensure it's installed in default location

**"Failed to build Docker images"**
- Check internet connection
- Ensure sufficient disk space (10GB+)
- Try running `docker system prune` to clean up space
- Check Docker Desktop memory allocation (8GB+ recommended)

**"Failed to export images"**
- Ensure sufficient disk space for 3.4GB of images
- Check that images were built successfully
- Verify Docker is still running

**"Failed to build installer"**
- Check that all tar files exist in docker-images/
- Verify Inno Setup script exists
- Ensure installer/output/ directory is writable

### Build Logs
Both scripts create detailed logs in `build.log`:
```bash
# View recent log entries
type build.log

# PowerShell
Get-Content build.log -Tail 20
```

### Manual Verification
```bash
# Check Docker images
docker images | findstr myshopcnc

# Check exported files
dir docker-images\*.tar

# Check installer
dir installer\output\*.exe
```

## 🎯 Advanced Usage

### Partial Builds
Use PowerShell script parameters for partial builds:

```powershell
# Only build images (skip export and installer)
.\build-complete-installer.ps1 -SkipExport -SkipInstaller

# Only create installer (use existing images)
.\build-complete-installer.ps1 -SkipBuild -SkipExport

# Build and export only (no installer)
.\build-complete-installer.ps1 -SkipInstaller
```

### Continuous Integration
For automated builds:

```bash
# Silent batch execution
build-complete-installer.bat > build-output.txt 2>&1

# PowerShell with error handling
powershell -ExecutionPolicy Bypass -File build-complete-installer.ps1 -ErrorAction Stop
```

### Custom Configuration
Modify these variables in the scripts:
- `PROJECT_NAME` - Change installer name
- `PROJECT_VERSION` - Update version number
- `DOCKER_IMAGES_DIR` - Change image export location
- `INSTALLER_DIR` - Change installer location

## 🎉 Success!

After successful execution, you'll have:
- ✅ Complete Windows installer ready for distribution
- ✅ All Docker images exported and ready
- ✅ Comprehensive build logs
- ✅ Validated installation package

The installer can be distributed to end users who only need Docker Desktop to run MyShopCNC!
