@echo off
title MyShopCNC - Build Installer

echo MyShopCNC Build Installer
echo =========================
echo.
echo This script will build the complete MyShopCNC installer from source.
echo.
echo Choose your preferred method:
echo.
echo 1. <PERSON><PERSON> (build-complete-installer.bat)
echo    - Simple, works on all Windows systems
echo    - Basic progress reporting
echo.
echo 2. PowerShell Script (build-complete-installer.ps1)
echo    - Advanced features and better error handling
echo    - Detailed logging and progress reporting
echo    - Supports command-line parameters
echo.
echo 3. Quick Build (skip prompts)
echo    - Runs batch script immediately
echo.

set /p choice="Enter your choice (1, 2, or 3): "

if "%choice%"=="1" goto :batch_script
if "%choice%"=="2" goto :powershell_script
if "%choice%"=="3" goto :quick_build

echo Invalid choice. Running batch script by default...
goto :batch_script

:batch_script
echo.
echo Running batch script...
echo.
call build-complete-installer.bat
goto :end

:powershell_script
echo.
echo Running PowerShell script...
echo.
echo Available PowerShell options:
echo   -SkipBuild      : Skip Docker image building
echo   -SkipExport     : Skip image export
echo   -SkipInstaller  : Skip installer creation
echo   -Verbose        : Show detailed output
echo.
set /p ps_params="Enter PowerShell parameters (or press Enter for default): "

if "%ps_params%"=="" (
    powershell.exe -ExecutionPolicy Bypass -File "build-complete-installer.ps1"
) else (
    powershell.exe -ExecutionPolicy Bypass -File "build-complete-installer.ps1" %ps_params%
)
goto :end

:quick_build
echo.
echo Running quick build...
echo.
call build-complete-installer.bat
goto :end

:end
echo.
echo Build process completed.
echo Check the output above for results.
echo.
pause
