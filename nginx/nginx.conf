worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout 65;

    upstream dashboardservice {
        server dashboardservice:3000;
    }

    upstream userservice {
        server userservice:3001;
    }

    server {
        listen 80;

        # Serve Angular frontend
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }

        # Proxy API calls to backend services
        location /api/dashboard/ {
            proxy_pass http://dashboardservice:3000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # File upload configuration
            client_max_body_size 10M;
            proxy_request_buffering off;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        location /api/user/ {
            proxy_pass http://userservice:3001/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}
