import { Component } from '@angular/core';
import { SnackbarService } from '../shared/services/snackbar.service';

@Component({
  selector: 'app-main-app',
  standalone: false,
  templateUrl: './main-app.component.html',
  styleUrl: './main-app.component.scss',
})
export class MainAppComponent {
  constructor(private snackbarService: SnackbarService) {
    this.snackbarService.open('Welcome to the Main App!', 'X', 'success');
  }
}
