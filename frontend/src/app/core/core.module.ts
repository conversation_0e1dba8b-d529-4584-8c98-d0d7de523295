import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatToolbarModule } from '@angular/material/toolbar';
import { FormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
const CORE_MATERIAL_MODULES = [
  MatButtonModule,
  MatCheckboxModule,
  MatToolbarModule,
  MatSnackBarModule,
  MatProgressSpinnerModule,
];
const CORE_MODULES = [FormsModule, ...CORE_MATERIAL_MODULES];

@NgModule({
  declarations: [],
  imports: [CommonModule, ...CORE_MODULES],
  exports: [...CORE_MODULES],
})
export class CoreModule {}
