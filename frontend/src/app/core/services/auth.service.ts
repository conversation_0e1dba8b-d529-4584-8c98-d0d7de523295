import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { jwtDecode } from 'jwt-decode';
import { Router } from '@angular/router';

export interface Jwt {
  exp: number;
  scope: string[];
  sub: string;
  name: string;
}

export interface User {
  username: string;
  password: string;
  email: string;
  firstName: string;
  lastName: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  user: User | null = null;
  constructor(
    private http: HttpClient,
    private router: Router
  ) {}

  get token(): string | null {
    return localStorage.getItem('token');
  }

  get isLoggedIn() {
    const token = this.token;
    if (!token) {
      return false;
    }
    return tokenIsValid(token);
  }

  login(username: string, password: string): Observable<any> {
    return of({
      token:
        'eyJraWQiOiIxOTIuMTY4LjIyLjIwIiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************.ZuxTmpmmDQh3Snas-faKJBpeEu6k20FtMzpWo8M-E3pi29BiYgIJYqgMyrrmi4n-XOnT6wWVrvbNAO42BRO59Hh-ZLuw_TPQ9z26ePdVbKZFWfZeEYYHM6PDN4N2m8ryoSJtN7X_l9_uvkCzGoY43rfuW4Cp4i5PTuXBUiwsQlNfL2keSlu2eBI0-bDA-8JU8eUqZsrVM00Fs_S81nzC5BeWDweisXC_7-1WBjXHB2fJccA7VvKlFAFINA8JWpXz095JmQ5PYFYJN65qMe6J3-inZYXEaZs2R9ZQAl3kXcrl5UJsrqQq4Cklx3b8GfUVvSrbJjsl3SntDPGa6ciBaA',
      user: {
        username: 'Narendra',
      },
    }).pipe(
      tap((res: any) => {
        this.setToken(res.token);
      })
    ); // Replace with actual login logic if needed
    return this.http.post('login', { username, password }).pipe(
      tap((res: any) => {
        this.setToken(res.access_token);
      })
    );
  }

  logout() {
    sessionStorage.clear();
    localStorage.clear();
    this.router.navigate(['login']);
  }

  private setToken(token: string) {
    if (tokenIsValid(token)) {
      localStorage.setItem('token', token);
    }
  }
}

function tokenIsValid(token: string) {
  const payload = jwtDecode(token);
  if (isJwt(payload)) {
    return isExpired(payload);
  }
  return false;
}

function isJwt(a: any): a is Jwt {
  // return typeof a === 'object' && a.exp !== undefined;
  return typeof a === 'object';
}

function isExpired(tokenPayload: Jwt) {
  return tokenPayload.exp >= Date.now() / 10000;
}
