<!-- src/app/login/login.component.html -->
<div class="login-container">
  <div class="login-split-wrapper">
    <!-- Left Section: Logo and Text -->
    <div class="left-section">
      <div class="logo-area">
        <!-- Using a simple SVG circle and text for the logo -->
        <svg
          class="logo-svg"
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="50" cy="50" r="40" stroke="#007BFF" stroke-width="8" />
          <circle cx="50" cy="50" r="20" stroke="#007BFF" stroke-width="8" />
        </svg>
        <span class="logo-text">myShop</span>
        <span class="logo-subtext">cnc</span>
      </div>
      <p class="section-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
      <p class="section-text">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
    </div>

    <!-- Right Section: Login Form -->
    <div class="right-section">
      <mat-card class="login-form-card">
        <mat-card-title class="card-title"
          >Please login to continue</mat-card-title
        >
        <mat-card-content>
          <form class="login-form" (ngSubmit)="onSubmit()" #loginForm="ngForm">
            <!-- Username Input -->
            <label class="form-label"
              >Username <span class="required">*</span></label
            >
            <mat-form-field appearance="outline" class="full-width-input">
              <input
                matInput
                required
                [(ngModel)]="username"
                name="username"
                #usernameInput="ngModel"
                autocomplete="username"
              />
              <mat-icon matPrefix>person_outline</mat-icon>
              <!-- Changed from 'person' to 'person_outline' for mockup match -->
              <!-- Error message for username -->
              <mat-error
                *ngIf="
                  usernameInput.invalid &&
                  (usernameInput.dirty || usernameInput.touched)
                "
              >
                <span
                  class="error-message"
                  *ngIf="usernameInput.errors?.['required']"
                  >Username is required.</span
                >
              </mat-error>
            </mat-form-field>
            <label class="form-label"
              >Password <span class="required">*</span></label
            >
            <!-- Password Input -->
            <mat-form-field appearance="outline" class="full-width-input">
              <input
                matInput
                [type]="hidePassword ? 'password' : 'text'"
                required
                [(ngModel)]="password"
                name="password"
                #passwordInput="ngModel"
                autocomplete="current-password"
              />
              <mat-icon matPrefix>lock_outline</mat-icon>
              <!-- Changed from default to 'lock_outline' for mockup match -->
              <button
                mat-icon-button
                matSuffix
                (click)="hidePassword = !hidePassword"
                [attr.aria-label]="'Toggle password visibility'"
                [attr.aria-pressed]="hidePassword"
                type="button"
              >
                <mat-icon>{{
                  hidePassword ? 'visibility_off' : 'visibility'
                }}</mat-icon>
              </button>
              <!-- Error message for password -->
              <mat-error
                *ngIf="
                  passwordInput.invalid &&
                  (passwordInput.dirty || passwordInput.touched)
                "
              >
                <span
                  *ngIf="passwordInput.errors?.['required']"
                  class="error-message"
                  >Password is required.</span
                >
              </mat-error>
            </mat-form-field>

            <!-- Remember Me Checkbox -->
            <mat-checkbox class="remember-me-checkbox"
              >Remember me</mat-checkbox
            >

            <!-- Login Button -->
            <button
              mat-flat-button
              color="primary"
              type="submit"
              class="login-button"
              [disabled]="loginForm.invalid || isLoading"
            >
              <span *ngIf="!isLoading">Log In</span>
              <!-- Changed from Login to Log In for mockup match -->
              <mat-progress-spinner
                *ngIf="isLoading"
                color="accent"
                mode="indeterminate"
                [diameter]="20"
              ></mat-progress-spinner>
            </button>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>
