import { Component } from '@angular/core';
import { AuthService } from '../../../core/services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  username: string = '';
  password: string = '';
  hidePassword: boolean = true;
  isLoading: boolean = false; // State for loading spinner

  constructor(
    private authService: AuthService,
    private router: Router
  ) {
    // if(this.authService.isLoggedIn) {
    //   this.router.navigate(['app']);
    // }
  }

  onSubmit(): void {
    if (this.isLoading) {
      return; // Prevent multiple submissions
    }
    this.isLoading = true;

    this.authService.login(this.username, this.password).subscribe({
      next: () => {
        this.isLoading = false;
        this.router.navigate(['app']);
      },
      error: (error) => {
        console.error('Login failed', error);

        this.isLoading = false; // Reset loading state on error
      },
    });
  }
}
