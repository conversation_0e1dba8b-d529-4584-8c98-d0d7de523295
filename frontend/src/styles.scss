/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;
@import './assets/themes/bluetheme.css';
@font-face {
  font-family: 'MontserratThin';
  src: url('./assets/Fonts/Montserrat/Montserrat-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratThinItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-ThinItalic.ttf')
    format('truetype');
  font-weight: 100;
  font-style: italic;
}

// /* ExtraLight - 200 */
@font-face {
  font-family: 'MontserratExtraLight';
  src: url('./assets/Fonts/Montserrat/Montserrat-ExtraLight.ttf')
    format('truetype');
  font-weight: 200;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratExtraLightItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-ExtraLightItalic.ttf')
    format('truetype');
  font-weight: 200;
  font-style: italic;
}

// /* Light - 300 */
@font-face {
  font-family: 'MontserratLight';
  src: url('./assets/Fonts/Montserrat/Montserrat-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratLightItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-LightItalic.ttf')
    format('truetype');
  font-weight: 300;
  font-style: italic;
}

// /* Regular - 400 */
@font-face {
  font-family: 'Montserrat';
  src: url('./assets/Fonts/Montserrat/Montserrat-Regular.ttf')
    format('truetype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
}

/* Medium - 500 */
@font-face {
  font-family: 'MontserratMedium';
  src: url('./assets/Fonts/Montserrat/Montserrat-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratMediumItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-MediumItalic.ttf')
    format('truetype');
  font-weight: 500;
  font-style: italic;
}

// /* SemiBold - 600 */
@font-face {
  font-family: 'MontserratSemiBold';
  src: url('./assets/Fonts/Montserrat/Montserrat-SemiBold.ttf')
    format('truetype');
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratSemiBoldItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-SemiBoldItalic.ttf')
    format('truetype');
  font-weight: 600;
  font-style: italic;
}

// /* Bold - 700 */
@font-face {
  font-family: 'MontserratBold';
  src: url('./assets/Fonts/Montserrat/Montserrat-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratBoldItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-BoldItalic.ttf')
    format('truetype');
  font-weight: 700;
  font-style: italic;
}

// /* ExtraBold - 800 */
@font-face {
  font-family: 'MontserratExtraBold';
  src: url('./assets/Fonts/Montserrat/Montserrat-ExtraBold.ttf')
    format('truetype');
  font-weight: 800;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratExtraBoldItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-ExtraBoldItalic.ttf')
    format('truetype');
  font-weight: 800;
  font-style: italic;
}

// /* Black - 900 */
@font-face {
  font-family: 'MontserratBlack';
  src: url('./assets/Fonts/Montserrat/Montserrat-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}
@font-face {
  font-family: 'MontserratBlackItalic';
  src: url('./assets/Fonts/Montserrat/Montserrat-BlackItalic.ttf')
    format('truetype');
  font-weight: 900;
  font-style: italic;
}

/* Global font use */
* {
  font-family: 'Montserrat';
}

html {
  color-scheme: light;
}

html.dark-mode {
  color-scheme: dark;
}

body.red-theme {
  @include mat.theme(
    (
      color: mat.$red-palette,
      typography: Montserrat,
      density: 0,
    )
  );
}

body.green-theme {
  @include mat.theme(
    (
      color: mat.$green-palette,
      typography: Montserrat,
      density: 0,
    )
  );
}

body.blue-theme {
  @include mat.theme(
    (
      typography: Montserrat,
      density: 0,
    )
  );
}

body.yellow-theme {
  @include mat.theme(
    (
      color: mat.$yellow-palette,
      typography: Montserrat,
      density: 0,
    )
  );
}

body.orange-theme {
  @include mat.theme(
    (
      color: mat.$orange-palette,
      typography: Montserrat,
      density: 0,
    )
  );
}

body.violet-theme {
  @include mat.theme(
    (
      color: mat.$violet-palette,
      typography: Montserrat,
      density: 0,
    )
  );
}

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: 'Montserrat', 'Helvetica Neue', sans-serif;
}
.mat-mdc-snack-bar-container.success-snk {
  --mdc-snackbar-container-color: white;
  --mdc-snackbar-supporting-text-color: rgba(0, 0, 0, 0.87);
  --mat-snack-bar-button-color: rgba(0, 0, 0, 0.6);
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  --mdc-snackbar-container-shape: 12px;
  border: 1px solid #28a745;
  border-left: 6px solid #28a745;
  border-radius: 12px;
  box-sizing: border-box;
  overflow: visible;
}
.success-snk .mdc-snackbar__surface {
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}
.mat-mdc-snack-bar-container.failed-snk {
  --mdc-snackbar-container-color: white;
  --mdc-snackbar-supporting-text-color: rgba(0, 0, 0, 0.87);
  --mat-snack-bar-button-color: rgba(0, 0, 0, 0.6);
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  --mdc-snackbar-container-shape: 12px;
  border: 1px solid red;
  border-left: 6px solid red;
  border-radius: 12px;
  box-sizing: border-box;
  overflow: visible;
}
.failed-snk .mdc-snackbar__surface {
  border-radius: 12px;
  overflow: visible;
  box-sizing: border-box;
}
.dark-mode .mat-mdc-snack-bar-container.success-snk {
  --mdc-snackbar-container-color: #1e1e1e;
  --mdc-snackbar-supporting-text-color: #e0ffe0;
  --mat-snack-bar-button-color: #88ff88;
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  border: 1px solid #28a745;
  border-left: 6px solid #28a745;
  border-radius: 12px;
}

.dark-mode .mat-mdc-snack-bar-container.failed-snk {
  --mdc-snackbar-container-color: #1e1e1e;
  --mdc-snackbar-supporting-text-color: #ffd6d6;
  --mat-snack-bar-button-color: #ff8888;
  --mdc-snackbar-supporting-text-font: 'MontserratMedium';
  border: 1px solid red;
  border-left: 6px solid red;
  border-radius: 12px;
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url('./assets/Icons/materialIcon.woff2') format('woff2');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
