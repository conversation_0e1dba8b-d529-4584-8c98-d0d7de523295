# MyShopCNC

A leading Machine Tool Solution provider's On-Premise Machine Tool Health Monitoring Solution called "MyShopCNC Software Application".

## 🚀 Quick Start

### Prerequisites
- **Docker Desktop** - [Download here](https://www.docker.com/products/docker-desktop/)
- **Git** - For cloning the repository
- **Node.js 18+** - For local development (optional)

### Getting Started
```bash
# Clone the repository
git clone https://github.com/your-org/your-repo.git
cd MyShopCNC-dev

# Start the application
docker-compose up --build

# Or use the automated build script
./build-installer.bat
```

### Access the Application
- **Frontend**: http://localhost:8080
- **API Gateway**: http://localhost
- **Database**: localhost:5432

## 🔧 Environment Setup

### Environment Files
Each service has its own environment configuration:

```bash
# Main environment file
cp env.dist .env

# Backend services
cp backend/env.dist backend/.env

# Frontend (if needed)
cp frontend/env.dist frontend/.env
```

### Docker Services
```bash
# Build and start all services
docker-compose up --build

# Start in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📁 Project Structure

```
MyShopCNC-dev/
├── backend/                        # Backend Services
│   ├── dashboardservice/           # Node.js Dashboard Service
│   │   ├── src/
│   │   │   ├── config/            # Database configuration
│   │   │   ├── routes/            # API route definitions
│   │   │   │   └── logoRoutes.js  # Logo management APIs
│   │   │   └── utils/             # Helper functions
│   │   ├── index.js               # Main service entry point
│   │   ├── package.json           # Dependencies
│   │   └── dockerfile             # Docker configuration
│   │
│   ├── userservice/               # User Management Service
│   │   ├── src/
│   │   ├── index.js
│   │   ├── package.json
│   │   └── dockerfile
│   │
│   └── .env                       # Backend environment variables
│
├── frontend/                      # Angular Frontend Application
│   ├── src/
│   │   ├── app/
│   │   ├── assets/
│   │   └── environments/
│   ├── package.json
│   ├── angular.json
│   └── dockerfile
│
├── nginx/                         # Reverse Proxy & Load Balancer
│   ├── nginx.conf                 # Nginx configuration
│   └── dockerfile
│
├── timescaledb/                   # Database Configuration
│   ├── migrations/
│   └── seed-data/
│
├── installer/                     # Windows Installer Components
│   ├── scripts/                   # PowerShell management scripts
│   ├── MyShopCNC-Setup.iss       # Inno Setup installer script
│   └── output/                    # Generated installer files
│
├── docker-images/                 # Exported Docker images (for installer)
├── docker-compose.yml             # Service orchestration
├── .env                          # Main environment variables
└── README.md                     # This file
```

## 🏗️ Architecture Overview

### Microservices Architecture
- **Frontend**: Angular SPA with Nginx
- **API Gateway**: Nginx reverse proxy
- **Dashboard Service**: Node.js/Express (Port 3000)
- **User Service**: Node.js/Express (Port 3001)
- **Database**: TimescaleDB (PostgreSQL + time-series)

### Service Communication
```
Frontend (Angular) 
    ↓ HTTP
Nginx Proxy 
    ↓ Load Balancing
Backend Services (Node.js)
    ↓ SQL
TimescaleDB
```

### Data Persistence
- **Database**: TimescaleDB volume (`timescale_data`)
- **Logo Storage**: File volume (`logo_uploads`)
- **Configuration**: Environment variables

## 🎯 Features

### ✅ Core Features
- **Machine Health Monitoring Dashboard**
- **User Management System**
- **Real-time Data Visualization**
- **Time-series Data Storage**
- **RESTful API Architecture**

### ✅ Logo Management System
- **Upload company logos** (JPEG, PNG, GIF, WebP)
- **Manage multiple logos** with versioning
- **Serve optimized images** with caching
- **RESTful API** for frontend integration
- **Persistent storage** in Docker volumes

### ✅ Deployment Features
- **Docker containerization** for all services
- **Windows installer** with embedded Docker images
- **Automated build scripts** for easy deployment
- **Volume persistence** for data retention

## 🔌 API Endpoints

### Dashboard Service
- `GET /health` - Service health check
- `GET /time` - Current database time
- `POST /api/dashboard/logo/upload` - Upload logo
- `GET /api/dashboard/logo/list` - Get all logos
- `GET /api/dashboard/logo/:filename` - Serve logo image
- `GET /api/dashboard/logo/current/active` - Get active logo
- `PUT /api/dashboard/logo/:filename` - Update logo
- `DELETE /api/dashboard/logo/:filename` - Delete logo

### User Service
- `GET /health` - Service health check
- Additional user management endpoints

## 🧪 Testing

### API Testing
```bash
# Test logo management APIs
./test-logo-api.sh

# Manual API testing
curl http://localhost/api/dashboard/health
curl -X POST -F "logo=@logo.png" http://localhost/api/dashboard/logo/upload
```

### Build Verification
```bash
# Verify build components
./verify-build.bat

# Test build scripts
./test-build-scripts.bat
```

## 🚀 Deployment

### Local Development
```bash
# Start development environment
docker-compose up --build

# View service logs
docker-compose logs -f dashboardservice
```

### Production Build
```bash
# Build complete installer
./build-complete-installer.bat

# Or use PowerShell version
powershell -ExecutionPolicy Bypass -File build-complete-installer.ps1
```

### Windows Installer
The project includes automated installer creation:
- **Complete build automation** from source to installer
- **Docker image packaging** (~3.4GB total)
- **Inno Setup integration** for professional installer
- **Management scripts** for easy operation

## 📋 Development Guidelines

### Code Quality
- **ESLint** and **Prettier** for code formatting
- **TypeScript** for type safety (frontend)
- **Consistent naming** conventions
- **Comprehensive error handling**

### Pre-commit Checks
```bash
# Backend linting
cd backend/dashboardservice
npm run lint
npm run format

# Frontend linting
cd frontend
npm run lint
npm run format
```

### Git Workflow
```bash
# Before committing
npm run lint
npm run format
git add .
git commit -m "feat: add new feature"
```

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
DB_HOST=timescaledb
DB_USER=postgres
DB_PASSWORD=root@123
DB_NAME=postgres
DB_PORT=5432

# Service Ports
DASHBOARD_PORT=3000
USER_PORT=3001
```

### Docker Volumes
- `timescale_data`: Database persistence
- `logo_uploads`: Logo file storage

## 📚 Documentation

- **[Build Instructions](BUILD-INSTRUCTIONS.md)** - Complete build guide
- **[Logo API Documentation](backend/dashboardservice/LOGO-API-README.md)** - Logo management APIs
- **[Build Scripts Guide](BUILD-SCRIPTS-README.md)** - Automated build documentation
- **[Installation Guide](installer/INSTALLATION-GUIDE.md)** - End-user installation

## 🆘 Troubleshooting

### Common Issues
1. **Docker not running**: Start Docker Desktop
2. **Port conflicts**: Check if ports 80, 3000, 3001, 5432 are available
3. **Build failures**: Ensure sufficient disk space (15GB+)
4. **Permission errors**: Run as administrator on Windows

### Getting Help
```bash
# Check service status
docker-compose ps

# View service logs
docker logs dashboardservice
docker logs userservice
docker logs timescale

# Check system resources
docker system df
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**MyShopCNC** - Empowering Machine Tool Health Monitoring
