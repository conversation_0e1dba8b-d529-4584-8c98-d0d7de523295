@echo off
title MyShopCNC - Build Verification

echo MyShopCNC Build Verification
echo =============================
echo.

set "ERRORS=0"

echo Checking project structure...
echo.

REM Check main files
if not exist "docker-compose.yml" (
    echo ✗ MISSING: docker-compose.yml
    set /a ERRORS+=1
) else (
    echo ✓ Found: docker-compose.yml
)

if not exist ".env" (
    echo ✗ MISSING: .env
    set /a ERRORS+=1
) else (
    echo ✓ Found: .env
)

REM Check directories
if not exist "frontend" (
    echo ✗ MISSING: frontend directory
    set /a ERRORS+=1
) else (
    echo ✓ Found: frontend directory
)

if not exist "backend" (
    echo ✗ MISSING: backend directory
    set /a ERRORS+=1
) else (
    echo ✓ Found: backend directory
)

if not exist "nginx" (
    echo ✗ MISSING: nginx directory
    set /a ERRORS+=1
) else (
    echo ✓ Found: nginx directory
)

echo.
echo Checking Docker images...
echo.

if not exist "docker-images" (
    echo ✗ MISSING: docker-images directory
    set /a ERRORS+=1
) else (
    echo ✓ Found: docker-images directory
    
    if not exist "docker-images\frontend.tar" (
        echo ✗ MISSING: frontend.tar
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: frontend.tar
    )
    
    if not exist "docker-images\nginx.tar" (
        echo ✗ MISSING: nginx.tar
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: nginx.tar
    )
    
    if not exist "docker-images\dashboardservice.tar" (
        echo ✗ MISSING: dashboardservice.tar
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: dashboardservice.tar
    )
    
    if not exist "docker-images\userservice.tar" (
        echo ✗ MISSING: userservice.tar
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: userservice.tar
    )
    
    if not exist "docker-images\timescaledb.tar" (
        echo ✗ MISSING: timescaledb.tar
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: timescaledb.tar
    )
)

echo.
echo Checking installer components...
echo.

if not exist "installer" (
    echo ✗ MISSING: installer directory
    set /a ERRORS+=1
) else (
    echo ✓ Found: installer directory
    
    if not exist "installer\MyShopCNC-Setup.iss" (
        echo ✗ MISSING: MyShopCNC-Setup.iss
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: MyShopCNC-Setup.iss
    )
    
    if not exist "installer\scripts" (
        echo ✗ MISSING: installer\scripts directory
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: installer\scripts directory
    )
    
    if not exist "installer\license.txt" (
        echo ✗ MISSING: license.txt
        set /a ERRORS+=1
    ) else (
        echo ✓ Found: license.txt
    )
)

echo.
echo Checking Docker images size...
if exist "docker-images" (
    for %%f in (docker-images\*.tar) do (
        echo   %%~nxf: %%~zf bytes
    )
)

echo.
echo =============================
if %ERRORS% EQU 0 (
    echo ✓ BUILD VERIFICATION PASSED!
    echo.
    echo All components are ready for installer creation.
    echo Run 'installer\build-installer.bat' to create the Windows installer.
    echo.
    echo Total Docker images size: ~3.4GB
    echo Expected installer size: ~3.4GB
) else (
    echo ✗ BUILD VERIFICATION FAILED!
    echo.
    echo Found %ERRORS% error(s). Please resolve before creating installer.
    echo.
    echo Common solutions:
    echo - Run 'docker-compose build' to build images
    echo - Run Docker export commands to create .tar files
    echo - Check that all directories exist
)

echo.
pause
