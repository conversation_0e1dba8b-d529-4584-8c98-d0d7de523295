import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = '/app/uploads/logos';
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `logo-${uniqueSuffix}${extension}`);
  }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
  }
};

// Configure multer with size limit (5MB)
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Utility function to get logo metadata
const getLogoMetadata = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    return {
      size: stats.size,
      uploadDate: stats.mtime,
      createdDate: stats.birthtime
    };
  } catch (error) {
    return null;
  }
};

// Utility function to list all logos
const listLogos = async () => {
  const logoDir = '/app/uploads/logos';
  try {
    const files = await fs.readdir(logoDir);
    const logoFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext);
    });

    const logosWithMetadata = await Promise.all(
      logoFiles.map(async (file) => {
        const filePath = path.join(logoDir, file);
        const metadata = await getLogoMetadata(filePath);
        return {
          filename: file,
          originalName: file,
          path: `/api/dashboard/logo/${file}`,
          ...metadata
        };
      })
    );

    return logosWithMetadata.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
  } catch (error) {
    console.error('Error listing logos:', error);
    return [];
  }
};

/**
 * POST /api/dashboard/logo/upload
 * Upload a new logo image
 */
router.post('/upload', upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const logoInfo = {
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: `/api/dashboard/logo/${req.file.filename}`,
      uploadDate: new Date()
    };

    console.log('Logo uploaded successfully:', logoInfo);

    res.status(201).json({
      success: true,
      message: 'Logo uploaded successfully',
      data: logoInfo
    });

  } catch (error) {
    console.error('Error uploading logo:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload logo',
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/logo/list
 * Get list of all uploaded logos
 */
router.get('/list', async (req, res) => {
  try {
    const logos = await listLogos();
    
    res.json({
      success: true,
      message: 'Logos retrieved successfully',
      data: logos,
      count: logos.length
    });

  } catch (error) {
    console.error('Error listing logos:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve logos',
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/logo/:filename
 * Serve a specific logo image
 */
router.get('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const logoPath = path.join('/app/uploads/logos', filename);

    // Check if file exists
    try {
      await fs.access(logoPath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'Logo not found'
      });
    }

    // Get file metadata
    const metadata = await getLogoMetadata(logoPath);
    if (!metadata) {
      return res.status(404).json({
        success: false,
        message: 'Logo not found'
      });
    }

    // Set appropriate headers
    const extension = path.extname(filename).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp'
    };

    const contentType = mimeTypes[extension] || 'application/octet-stream';
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Length', metadata.size);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
    res.setHeader('Last-Modified', metadata.uploadDate.toUTCString());

    // Stream the file
    const fileStream = await fs.readFile(logoPath);
    res.send(fileStream);

  } catch (error) {
    console.error('Error serving logo:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to serve logo',
      error: error.message
    });
  }
});

/**
 * PUT /api/dashboard/logo/:filename
 * Update/replace a specific logo
 */
router.put('/:filename', upload.single('logo'), async (req, res) => {
  try {
    const { filename } = req.params;
    const oldLogoPath = path.join('/app/uploads/logos', filename);

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Check if old logo exists and delete it
    try {
      await fs.access(oldLogoPath);
      await fs.unlink(oldLogoPath);
      console.log('Old logo deleted:', filename);
    } catch (error) {
      console.log('Old logo not found, proceeding with upload');
    }

    // Rename new file to match the requested filename
    const newLogoPath = path.join('/app/uploads/logos', filename);
    await fs.rename(req.file.path, newLogoPath);

    const logoInfo = {
      filename: filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: `/api/dashboard/logo/${filename}`,
      updateDate: new Date()
    };

    console.log('Logo updated successfully:', logoInfo);

    res.json({
      success: true,
      message: 'Logo updated successfully',
      data: logoInfo
    });

  } catch (error) {
    console.error('Error updating logo:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update logo',
      error: error.message
    });
  }
});

/**
 * DELETE /api/dashboard/logo/:filename
 * Delete a specific logo
 */
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const logoPath = path.join('/app/uploads/logos', filename);

    // Check if file exists
    try {
      await fs.access(logoPath);
    } catch (error) {
      return res.status(404).json({
        success: false,
        message: 'Logo not found'
      });
    }

    // Delete the file
    await fs.unlink(logoPath);
    console.log('Logo deleted successfully:', filename);

    res.json({
      success: true,
      message: 'Logo deleted successfully',
      filename: filename
    });

  } catch (error) {
    console.error('Error deleting logo:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete logo',
      error: error.message
    });
  }
});

/**
 * GET /api/dashboard/logo/current/active
 * Get the currently active logo (most recently uploaded)
 */
router.get('/current/active', async (req, res) => {
  try {
    const logos = await listLogos();
    
    if (logos.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No logos found'
      });
    }

    // Return the most recently uploaded logo
    const activeLogo = logos[0];

    res.json({
      success: true,
      message: 'Active logo retrieved successfully',
      data: activeLogo
    });

  } catch (error) {
    console.error('Error getting active logo:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get active logo',
      error: error.message
    });
  }
});

export default router;
