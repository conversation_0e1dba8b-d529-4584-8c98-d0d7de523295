# Logo Management API Documentation

This document describes the logo management APIs added to the Dashboard Service for uploading, retrieving, updating, and deleting logo images.

## Overview

The logo management system allows the Angular frontend to:
- Upload logo images to the server
- Retrieve lists of uploaded logos
- Get the currently active logo
- Update/replace existing logos
- Delete logos
- Serve logo images with proper caching

All logo files are stored in a Docker volume (`logo_uploads`) mounted at `/app/uploads` in the container.

## API Endpoints

### Base URL
All logo APIs are available under: `/api/dashboard/logo`

### 1. Upload Logo

**POST** `/api/dashboard/logo/upload`

Upload a new logo image to the server.

**Request:**
- Method: `POST`
- Content-Type: `multipart/form-data`
- Body: Form data with `logo` field containing the image file

**Response:**
```json
{
  "success": true,
  "message": "Logo uploaded successfully",
  "data": {
    "filename": "logo-1640995200000-*********.png",
    "originalName": "company-logo.png",
    "mimetype": "image/png",
    "size": 45678,
    "path": "/api/dashboard/logo/logo-1640995200000-*********.png",
    "uploadDate": "2023-12-31T12:00:00.000Z"
  }
}
```

**File Restrictions:**
- Allowed types: JPEG, PNG, GIF, WebP
- Maximum size: 5MB
- Files are automatically renamed with timestamp for uniqueness

### 2. Get Logo List

**GET** `/api/dashboard/logo/list`

Retrieve a list of all uploaded logos, sorted by upload date (newest first).

**Response:**
```json
{
  "success": true,
  "message": "Logos retrieved successfully",
  "data": [
    {
      "filename": "logo-1640995200000-*********.png",
      "originalName": "company-logo.png",
      "path": "/api/dashboard/logo/logo-1640995200000-*********.png",
      "size": 45678,
      "uploadDate": "2023-12-31T12:00:00.000Z",
      "createdDate": "2023-12-31T12:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 3. Get Specific Logo

**GET** `/api/dashboard/logo/:filename`

Serve a specific logo image file.

**Parameters:**
- `filename`: The filename of the logo to retrieve

**Response:**
- Content-Type: Appropriate image MIME type
- Headers: Caching headers for optimal performance
- Body: Binary image data

**Example:**
```
GET /api/dashboard/logo/logo-1640995200000-*********.png
```

### 4. Get Active Logo

**GET** `/api/dashboard/logo/current/active`

Get the currently active logo (most recently uploaded).

**Response:**
```json
{
  "success": true,
  "message": "Active logo retrieved successfully",
  "data": {
    "filename": "logo-1640995200000-*********.png",
    "originalName": "company-logo.png",
    "path": "/api/dashboard/logo/logo-1640995200000-*********.png",
    "size": 45678,
    "uploadDate": "2023-12-31T12:00:00.000Z"
  }
}
```

### 5. Update Logo

**PUT** `/api/dashboard/logo/:filename`

Replace an existing logo with a new image file.

**Parameters:**
- `filename`: The filename of the logo to replace

**Request:**
- Method: `PUT`
- Content-Type: `multipart/form-data`
- Body: Form data with `logo` field containing the new image file

**Response:**
```json
{
  "success": true,
  "message": "Logo updated successfully",
  "data": {
    "filename": "logo-1640995200000-*********.png",
    "originalName": "new-company-logo.png",
    "mimetype": "image/png",
    "size": 52341,
    "path": "/api/dashboard/logo/logo-1640995200000-*********.png",
    "updateDate": "2023-12-31T13:00:00.000Z"
  }
}
```

### 6. Delete Logo

**DELETE** `/api/dashboard/logo/:filename`

Delete a specific logo file.

**Parameters:**
- `filename`: The filename of the logo to delete

**Response:**
```json
{
  "success": true,
  "message": "Logo deleted successfully",
  "filename": "logo-1640995200000-*********.png"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `201` - Created (for uploads)
- `400` - Bad Request (invalid file, missing data)
- `404` - Not Found (logo doesn't exist)
- `500` - Internal Server Error

## File Storage

### Volume Configuration
Logo files are stored in a Docker volume for persistence:

```yaml
# docker-compose.yml
volumes:
  logo_uploads:
    driver: local

services:
  dashboardservice:
    volumes:
      - logo_uploads:/app/uploads
```

### Directory Structure
```
/app/uploads/
└── logos/
    ├── logo-1640995200000-*********.png
    ├── logo-1640995300000-987654321.jpg
    └── ...
```

### File Naming
- Original filenames are preserved in metadata
- Actual files are renamed with timestamp and random suffix
- Format: `logo-{timestamp}-{random}.{extension}`

## Frontend Integration

### Angular Service
Use the provided `LogoService` (see `frontend-logo-service-example.ts`) for easy integration:

```typescript
// Upload a logo
this.logoService.uploadLogo(file).subscribe(result => {
  console.log('Uploaded:', result);
});

// Get active logo
this.logoService.getActiveLogo().subscribe(logo => {
  this.currentLogo = logo;
});

// Display logo in template
<img [src]="logoService.getLogoUrl(logo.filename)" [alt]="logo.originalName">
```

### File Validation
The service includes client-side validation:
- File type checking
- Size limit enforcement (5MB)
- Error handling

## Security Considerations

1. **File Type Validation**: Only image files are accepted
2. **Size Limits**: 5MB maximum file size
3. **Unique Filenames**: Prevents file conflicts and overwrites
4. **CORS Enabled**: Allows frontend access
5. **Volume Isolation**: Files stored in dedicated volume

## Performance Features

1. **Caching Headers**: Images served with long-term cache headers
2. **Efficient Streaming**: Files streamed directly from disk
3. **Metadata Caching**: File information cached for list operations
4. **Sorted Results**: Logos sorted by upload date for optimal UX

## Usage Examples

### cURL Examples

**Upload a logo:**
```bash
curl -X POST \
  -F "logo=@/path/to/logo.png" \
  http://localhost/api/dashboard/logo/upload
```

**Get logo list:**
```bash
curl http://localhost/api/dashboard/logo/list
```

**Get active logo:**
```bash
curl http://localhost/api/dashboard/logo/current/active
```

**Delete a logo:**
```bash
curl -X DELETE http://localhost/api/dashboard/logo/logo-1640995200000-*********.png
```

## Troubleshooting

### Common Issues

1. **"No file uploaded"**
   - Ensure the form field is named `logo`
   - Check that the file is properly selected

2. **"Invalid file type"**
   - Only JPEG, PNG, GIF, and WebP are allowed
   - Check the file's MIME type

3. **"File size too large"**
   - Maximum size is 5MB
   - Compress the image before uploading

4. **"Logo not found"**
   - Verify the filename exists
   - Check that the file wasn't deleted

### Logs
Check the dashboard service logs for detailed error information:
```bash
docker logs dashboardservice
```
