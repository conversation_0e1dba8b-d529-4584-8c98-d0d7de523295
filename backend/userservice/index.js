import express from 'express';
import pkg from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __filename and __dirname in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Now load .env from project root (3 levels up)
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const { Pool } = pkg;

const pool = new Pool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT || 5432,
});

// Test DB connection at startup
(async () => {
  try {
    await pool.query('SELECT 1');
    console.log('DB connection successful');
  } catch (err) {
    console.error('DB connection error:', err.message);
  }
})();

const app = express();

app.get('/health', (_, res) =>
  res.json({ ok: true, service: 'dashboard-service' })
);

app.get('/time', async (_, res, next) => {
  try {
    const { rows } = await pool.query('SELECT NOW()');
    res.json(rows[0]);
  } catch (e) {
    next(e);
  }
});

const port = process.env.PORT || 3001;
app.listen(port, () => console.log(`User-service running on :${port}`));
